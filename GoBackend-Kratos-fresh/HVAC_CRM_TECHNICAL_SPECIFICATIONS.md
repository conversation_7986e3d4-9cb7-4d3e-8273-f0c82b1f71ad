# 🔧 **HVAC CRM - <PERSON>EC<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> TECHNICZNE I IMPLEMENTACJA**

## 📋 **TECHNICAL ARCHITECTURE OVERVIEW**

### 🏗️ **SYSTEM ARCHITECTURE**

#### **Backend Architecture (Go)**
```
🔧 BACKEND STACK:
├── Go 1.21+ (Core Language)
├── Gorilla Mux (HTTP Router)
├── GORM (ORM Framework)
├── PostgreSQL 15+ (Primary Database)
├── Redis 7+ (Caching & Sessions)
├── MinIO (Object Storage)
├── Docker & Kubernetes (Containerization)
└── CORS & Security Middleware
```

#### **Frontend Architecture**
```
🎨 FRONTEND STACK:
├── SolidJS (Primary Framework)
├── TypeScript (Type Safety)
├── Tailwind CSS (Styling)
├── Chart.js (Data Visualization)
├── D3.js (Advanced Visualizations)
├── PWA Support (Mobile Experience)
└── Responsive Design (Mobile-First)
```

#### **Database Schema Design**
```sql
-- Core CRM Tables
CREATE TABLE customers (
    id BIGSERIAL PRIMARY KEY,
    name VARCHA<PERSON>(255) NOT NULL,
    email VARCHAR(255) UNIQUE,
    phone VARCHAR(50),
    address TEXT,
    status VARCHAR(50) DEFAULT 'active',
    priority INTEGER DEFAULT 2,
    created_at TIMESTAMP DEFAULT NOW(),
    updated_at TIMESTAMP DEFAULT NOW()
);

CREATE TABLE leads (
    id BIGSERIAL PRIMARY KEY,
    name VARCHAR(255) NOT NULL,
    email VARCHAR(255),
    phone VARCHAR(50),
    source VARCHAR(100),
    status VARCHAR(50) DEFAULT 'new',
    score INTEGER DEFAULT 0,
    assigned_to BIGINT REFERENCES users(id),
    converted_at TIMESTAMP,
    customer_id BIGINT REFERENCES customers(id),
    created_at TIMESTAMP DEFAULT NOW()
);

CREATE TABLE service_orders (
    id BIGSERIAL PRIMARY KEY,
    customer_id BIGINT REFERENCES customers(id),
    title VARCHAR(255) NOT NULL,
    description TEXT,
    status VARCHAR(50) DEFAULT 'new',
    priority INTEGER DEFAULT 2,
    technician_id BIGINT REFERENCES users(id),
    scheduled_at TIMESTAMP,
    completed_at TIMESTAMP,
    total_cost DECIMAL(10,2),
    created_at TIMESTAMP DEFAULT NOW()
);

CREATE TABLE equipment (
    id BIGSERIAL PRIMARY KEY,
    customer_id BIGINT REFERENCES customers(id),
    type VARCHAR(100) NOT NULL,
    brand VARCHAR(100),
    model VARCHAR(100),
    serial_number VARCHAR(100) UNIQUE,
    install_date DATE,
    warranty_end DATE,
    status VARCHAR(50) DEFAULT 'active',
    health_score INTEGER DEFAULT 100,
    location TEXT,
    created_at TIMESTAMP DEFAULT NOW()
);

CREATE TABLE email_analysis (
    id BIGSERIAL PRIMARY KEY,
    email_id BIGINT REFERENCES emails(id),
    sentiment VARCHAR(50),
    intent VARCHAR(100),
    priority VARCHAR(50),
    confidence DECIMAL(3,2),
    ai_summary TEXT,
    created_at TIMESTAMP DEFAULT NOW()
);
```

---

## 🔧 **API SPECIFICATIONS**

### **RESTful API Design**

#### **Unified Response Format**
```json
{
  "data": "Main content or array of objects",
  "meta": {
    "total": 156,
    "page": 1,
    "per_page": 20,
    "has_next": true,
    "has_prev": false,
    "query_time": "25ms",
    "data_freshness": "real-time"
  },
  "ui": {
    "formatting": {
      "currency": "PLN",
      "date": "relative",
      "phone": "international"
    },
    "actions": [
      {
        "id": "call",
        "label": "Zadzwoń",
        "icon": "📞",
        "color": "#17a2b8",
        "endpoint": "/api/customers/{id}/call",
        "method": "POST"
      }
    ],
    "filters": [
      {
        "field": "status",
        "label": "Status",
        "type": "select",
        "options": ["active", "inactive", "vip"],
        "default": "active"
      }
    ],
    "visualizations": [
      {
        "type": "chart",
        "config": {
          "type": "line",
          "field": "revenue",
          "period": "month"
        }
      }
    ]
  },
  "context": {
    "user_role": "manager",
    "current_time": "10:51",
    "timezone": "Europe/Warsaw",
    "permissions": ["read", "write", "delete"]
  },
  "timestamp": "2024-05-29T10:51:23Z"
}
```

#### **Core API Endpoints**

**Customer Management:**
```
GET    /api/customers              # List customers with pagination
POST   /api/customers              # Create new customer
GET    /api/customers/{id}         # Get customer details
PUT    /api/customers/{id}         # Update customer
DELETE /api/customers/{id}         # Delete customer
GET    /api/customers/enhanced     # Enhanced customer list with metadata
GET    /api/customers/{id}/enhanced # Enhanced customer details
GET    /api/customers/{id}/communications # Customer communication history
GET    /api/customers/{id}/equipment      # Customer equipment list
GET    /api/customers/{id}/service-history # Service history
GET    /api/customers/{id}/financial      # Financial summary
```

**Lead Management:**
```
GET    /api/leads                  # List leads with filtering
POST   /api/leads                  # Create new lead
GET    /api/leads/{id}             # Get lead details
PUT    /api/leads/{id}             # Update lead
POST   /api/leads/{id}/convert     # Convert lead to customer
PUT    /api/leads/{id}/score       # Update lead score
GET    /api/leads/campaigns        # Lead campaigns overview
GET    /api/leads/sources          # Lead sources analytics
```

**Service Management:**
```
GET    /api/service-orders         # List service orders
POST   /api/service-orders         # Create service order
GET    /api/service-orders/{id}    # Get service order details
PUT    /api/service-orders/{id}    # Update service order
POST   /api/service-orders/{id}/complete # Complete service order
PUT    /api/service-orders/{id}/assign   # Assign technician
GET    /api/service/schedule       # Service schedule overview
GET    /api/service/technicians    # Technicians list and availability
```

**Equipment Management:**
```
GET    /api/equipment              # List equipment with filtering
POST   /api/equipment              # Register new equipment
GET    /api/equipment/{id}         # Get equipment details
PUT    /api/equipment/{id}         # Update equipment
GET    /api/equipment/{id}/health  # Equipment health status
GET    /api/equipment/{id}/maintenance # Maintenance history
GET    /api/equipment/categories   # Equipment categories
GET    /api/equipment/health-overview  # Overall health overview
```

---

## 🔒 **SECURITY & AUTHENTICATION**

### **Security Framework**

#### **Authentication & Authorization**
```go
// JWT Token Structure
type JWTClaims struct {
    UserID   int64    `json:"user_id"`
    Email    string   `json:"email"`
    Role     string   `json:"role"`
    Permissions []string `json:"permissions"`
    jwt.StandardClaims
}

// Role-Based Access Control
type Permission struct {
    Resource string `json:"resource"` // customers, leads, service_orders
    Action   string `json:"action"`   // read, write, delete, admin
}

// Security Middleware
func AuthMiddleware(next http.Handler) http.Handler {
    return http.HandlerFunc(func(w http.ResponseWriter, r *http.Request) {
        token := extractToken(r)
        claims, err := validateJWT(token)
        if err != nil {
            http.Error(w, "Unauthorized", http.StatusUnauthorized)
            return
        }
        
        ctx := context.WithValue(r.Context(), "user", claims)
        next.ServeHTTP(w, r.WithContext(ctx))
    })
}
```

#### **Data Encryption**
```go
// Database Encryption
type EncryptedField struct {
    Value     string `gorm:"column:encrypted_value"`
    IV        string `gorm:"column:iv"`
    Algorithm string `gorm:"column:algorithm"`
}

// API Communication Security
func setupTLS() *tls.Config {
    return &tls.Config{
        MinVersion:               tls.VersionTLS12,
        CurvePreferences:         []tls.CurveID{tls.CurveP521, tls.CurveP384, tls.CurveP256},
        PreferServerCipherSuites: true,
        CipherSuites: []uint16{
            tls.TLS_ECDHE_RSA_WITH_AES_256_GCM_SHA384,
            tls.TLS_ECDHE_RSA_WITH_CHACHA20_POLY1305,
            tls.TLS_ECDHE_RSA_WITH_AES_128_GCM_SHA256,
        },
    }
}
```

---

## 📊 **DATA PROCESSING & ANALYTICS**

### **AI & Machine Learning Integration**

#### **Email Intelligence Processing**
```go
// Email Analysis Pipeline
type EmailAnalyzer struct {
    SentimentModel    *ml.Model
    IntentClassifier  *ml.Model
    PriorityScorer    *ml.Model
    LanguageDetector  *ml.Model
}

func (ea *EmailAnalyzer) AnalyzeEmail(email *Email) (*EmailAnalysis, error) {
    // Sentiment Analysis
    sentiment := ea.SentimentModel.Predict(email.Content)
    
    // Intent Classification
    intent := ea.IntentClassifier.Predict(email.Subject + " " + email.Content)
    
    // Priority Scoring
    priority := ea.PriorityScorer.Predict(email)
    
    // Generate AI Summary
    summary := ea.generateSummary(email.Content)
    
    return &EmailAnalysis{
        EmailID:    email.ID,
        Sentiment:  sentiment,
        Intent:     intent,
        Priority:   priority,
        AISummary:  summary,
        Confidence: calculateConfidence(sentiment, intent, priority),
    }, nil
}
```

#### **Predictive Maintenance Algorithm**
```go
// Predictive Maintenance Engine
type PredictiveEngine struct {
    FailureModel     *ml.Model
    PerformanceModel *ml.Model
    IoTDataProcessor *iot.Processor
}

func (pe *PredictiveEngine) PredictFailure(equipment *Equipment) (*FailurePrediction, error) {
    // Collect IoT sensor data
    sensorData := pe.IoTDataProcessor.GetLatestData(equipment.ID)
    
    // Feature engineering
    features := pe.extractFeatures(equipment, sensorData)
    
    // Predict failure probability
    failureProb := pe.FailureModel.Predict(features)
    
    // Estimate time to failure
    timeToFailure := pe.estimateTimeToFailure(features, failureProb)
    
    return &FailurePrediction{
        EquipmentID:     equipment.ID,
        FailureRisk:     failureProb,
        TimeToFailure:   timeToFailure,
        RecommendedAction: pe.generateRecommendation(failureProb),
        Confidence:      pe.calculateConfidence(features),
    }, nil
}
```

---

## 📱 **MOBILE & FRONTEND SPECIFICATIONS**

### **Progressive Web App (PWA)**

#### **Service Worker Configuration**
```javascript
// service-worker.js
const CACHE_NAME = 'hvac-crm-v1.0.0';
const urlsToCache = [
    '/',
    '/static/css/main.css',
    '/static/js/main.js',
    '/api/dashboard/overview',
    '/api/customers/enhanced'
];

self.addEventListener('install', event => {
    event.waitUntil(
        caches.open(CACHE_NAME)
            .then(cache => cache.addAll(urlsToCache))
    );
});

self.addEventListener('fetch', event => {
    event.respondWith(
        caches.match(event.request)
            .then(response => {
                // Return cached version or fetch from network
                return response || fetch(event.request);
            })
    );
});
```

#### **Offline Functionality**
```javascript
// offline-manager.js
class OfflineManager {
    constructor() {
        this.syncQueue = [];
        this.isOnline = navigator.onLine;
        this.setupEventListeners();
    }
    
    setupEventListeners() {
        window.addEventListener('online', () => {
            this.isOnline = true;
            this.syncPendingData();
        });
        
        window.addEventListener('offline', () => {
            this.isOnline = false;
        });
    }
    
    async saveOfflineData(endpoint, data) {
        const offlineData = {
            endpoint,
            data,
            timestamp: Date.now(),
            method: 'POST'
        };
        
        this.syncQueue.push(offlineData);
        await this.saveToIndexedDB(offlineData);
    }
    
    async syncPendingData() {
        for (const item of this.syncQueue) {
            try {
                await fetch(item.endpoint, {
                    method: item.method,
                    headers: { 'Content-Type': 'application/json' },
                    body: JSON.stringify(item.data)
                });
                
                // Remove from queue after successful sync
                this.syncQueue = this.syncQueue.filter(i => i !== item);
            } catch (error) {
                console.error('Sync failed:', error);
            }
        }
    }
}
```

---

## 🔧 **DEPLOYMENT & INFRASTRUCTURE**

### **Docker Configuration**

#### **Backend Dockerfile**
```dockerfile
# Multi-stage build for Go backend
FROM golang:1.21-alpine AS builder

WORKDIR /app
COPY go.mod go.sum ./
RUN go mod download

COPY . .
RUN CGO_ENABLED=0 GOOS=linux go build -o unified-hvac-crm cmd/unified-hvac-crm/main.go

FROM alpine:latest
RUN apk --no-cache add ca-certificates tzdata
WORKDIR /root/

COPY --from=builder /app/unified-hvac-crm .
COPY --from=builder /app/web ./web

EXPOSE 8080
CMD ["./unified-hvac-crm"]
```

#### **Docker Compose Configuration**
```yaml
version: '3.8'

services:
  hvac-crm:
    build: .
    ports:
      - "8080:8080"
    environment:
      - DB_HOST=postgres
      - DB_USER=hvacdb
      - DB_PASSWORD=blaeritipol
      - DB_NAME=hvacdb
      - REDIS_URL=redis:6379
      - MINIO_ENDPOINT=minio:9000
    depends_on:
      - postgres
      - redis
      - minio

  postgres:
    image: postgres:15-alpine
    environment:
      - POSTGRES_DB=hvacdb
      - POSTGRES_USER=hvacdb
      - POSTGRES_PASSWORD=blaeritipol
    volumes:
      - postgres_data:/var/lib/postgresql/data
    ports:
      - "5432:5432"

  redis:
    image: redis:7-alpine
    ports:
      - "6379:6379"
    volumes:
      - redis_data:/data

  minio:
    image: minio/minio:latest
    command: server /data --console-address ":9001"
    environment:
      - MINIO_ROOT_USER=koldbringer
      - MINIO_ROOT_PASSWORD=Blaeritipol1
    ports:
      - "9000:9000"
      - "9001:9001"
    volumes:
      - minio_data:/data

volumes:
  postgres_data:
  redis_data:
  minio_data:
```

### **Kubernetes Deployment**
```yaml
apiVersion: apps/v1
kind: Deployment
metadata:
  name: hvac-crm-deployment
spec:
  replicas: 3
  selector:
    matchLabels:
      app: hvac-crm
  template:
    metadata:
      labels:
        app: hvac-crm
    spec:
      containers:
      - name: hvac-crm
        image: hvac-crm:latest
        ports:
        - containerPort: 8080
        env:
        - name: DB_HOST
          value: "postgres-service"
        - name: REDIS_URL
          value: "redis-service:6379"
        resources:
          requests:
            memory: "256Mi"
            cpu: "250m"
          limits:
            memory: "512Mi"
            cpu: "500m"
---
apiVersion: v1
kind: Service
metadata:
  name: hvac-crm-service
spec:
  selector:
    app: hvac-crm
  ports:
  - protocol: TCP
    port: 80
    targetPort: 8080
  type: LoadBalancer
```

---

## 📊 **MONITORING & PERFORMANCE**

### **Performance Metrics**
```go
// Performance monitoring middleware
func PerformanceMiddleware(next http.Handler) http.Handler {
    return http.HandlerFunc(func(w http.ResponseWriter, r *http.Request) {
        start := time.Now()
        
        // Wrap ResponseWriter to capture status code
        wrapped := &responseWriter{ResponseWriter: w, statusCode: 200}
        
        next.ServeHTTP(wrapped, r)
        
        duration := time.Since(start)
        
        // Log performance metrics
        log.Printf("Method: %s, Path: %s, Status: %d, Duration: %v",
            r.Method, r.URL.Path, wrapped.statusCode, duration)
        
        // Send metrics to monitoring system
        metrics.RecordHTTPRequest(r.Method, r.URL.Path, wrapped.statusCode, duration)
    })
}
```

### **Health Check Endpoints**
```go
func handleHealthCheck(w http.ResponseWriter, r *http.Request) {
    health := HealthStatus{
        Status:    "healthy",
        Timestamp: time.Now(),
        Checks: map[string]interface{}{
            "database":    checkDatabase(),
            "redis":       checkRedis(),
            "minio":       checkMinIO(),
            "memory":      getMemoryUsage(),
            "disk":        getDiskUsage(),
        },
    }
    
    w.Header().Set("Content-Type", "application/json")
    json.NewEncoder(w).Encode(health)
}
```

---

## 🎯 **IMPLEMENTATION TIMELINE**

### **Phase 1: Core System (Completed ✅)**
- Unified Dashboard Interface
- Basic CRM Modules
- Database Integration
- API Framework
- Security Implementation

### **Phase 2: Enhanced Features (In Progress 🔄)**
- Real Database Operations
- Advanced Search & Filtering
- Mobile Responsiveness
- Performance Optimization
- Email Intelligence

### **Phase 3: Advanced Integration (Planned 📅)**
- Mobile Applications
- IoT Integration
- Predictive Analytics
- Third-Party APIs
- Customer Portal

**🚀 TECHNICAL FOUNDATION READY FOR ENTERPRISE DEPLOYMENT!** ⚡
