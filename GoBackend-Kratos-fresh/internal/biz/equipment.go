package biz

import (
	"context"
	"time"

	"gobackend-hvac-kratos/internal/entity"

	"github.com/go-kratos/kratos/v2/log"
)

// 🏭 Equipment Business Logic - HVAC Equipment Management
// GoBackend-Kratos HVAC CRM System

// EquipmentRepo defines the interface for equipment data operations
type EquipmentRepo interface {
	// Equipment CRUD
	CreateEquipment(ctx context.Context, equipment *entity.Equipment) (*entity.Equipment, error)
	GetEquipment(ctx context.Context, id int64) (*entity.Equipment, error)
	ListEquipment(ctx context.Context, page, pageSize int32, customerID int64, equipmentType, status, healthStatus string) ([]*entity.Equipment, int32, error)
	UpdateEquipment(ctx context.Context, equipment *entity.Equipment) (*entity.Equipment, error)
	DeleteEquipment(ctx context.Context, id int64) error

	// Health Management
	GetEquipmentHealth(ctx context.Context, equipmentID int64) (*entity.Equipment, []*entity.HealthMetric, error)
	UpdateEquipmentHealth(ctx context.Context, equipmentID int64, metrics []*entity.HealthMetric) error
	GetHealthTrends(ctx context.Context, equipmentID int64, metricName string, days int) ([]*entity.HealthMetric, error)

	// Maintenance Management
	ScheduleMaintenance(ctx context.Context, schedule *entity.MaintenanceSchedule) (*entity.MaintenanceSchedule, error)
	GetMaintenanceHistory(ctx context.Context, equipmentID int64, page, pageSize int32, maintenanceType string) ([]*entity.MaintenanceRecord, int32, error)
	CreateMaintenanceRecord(ctx context.Context, record *entity.MaintenanceRecord) (*entity.MaintenanceRecord, error)

	// Parts Management
	GetEquipmentParts(ctx context.Context, equipmentID int64) ([]*entity.EquipmentPart, error)
	UpdatePartStatus(ctx context.Context, equipmentID, partID int64, status string, replacementDate *time.Time, notes string) (*entity.EquipmentPart, error)

	// Analytics
	GetEquipmentAnalytics(ctx context.Context, equipmentID int64, startDate, endDate time.Time) (*entity.EquipmentAnalytics, error)
	GetFleetOverview(ctx context.Context, customerID int64, equipmentType string) (*entity.FleetOverview, error)
}

// EquipmentUsecase encapsulates equipment business logic
type EquipmentUsecase struct {
	repo EquipmentRepo
	log  *log.Helper
}

// NewEquipmentUsecase creates a new equipment use case
func NewEquipmentUsecase(repo EquipmentRepo, logger log.Logger) *EquipmentUsecase {
	return &EquipmentUsecase{
		repo: repo,
		log:  log.NewHelper(logger),
	}
}

// ============================================================================
// EQUIPMENT CRUD OPERATIONS
// ============================================================================

// CreateEquipment creates a new equipment with validation
func (uc *EquipmentUsecase) CreateEquipment(ctx context.Context, equipment *entity.Equipment) (*entity.Equipment, error) {
	uc.log.WithContext(ctx).Infof("Creating equipment: %s", equipment.Name)

	// Business logic validation
	if equipment.Name == "" {
		return nil, ErrEquipmentNameRequired
	}
	if equipment.CustomerID <= 0 {
		return nil, ErrInvalidCustomerID
	}
	if equipment.Type == "" {
		return nil, ErrEquipmentTypeRequired
	}
	if equipment.SerialNumber == "" {
		return nil, ErrEquipmentSerialRequired
	}

	// Set defaults
	if equipment.Status == "" {
		equipment.Status = entity.EquipmentStatusActive
	}
	if equipment.HealthScore == 0 {
		equipment.HealthScore = 1.0 // New equipment starts with perfect health
	}
	if equipment.HealthStatus == "" {
		equipment.HealthStatus = "excellent"
	}

	// Set timestamps
	now := time.Now()
	equipment.CreatedAt = now
	equipment.UpdatedAt = now

	return uc.repo.CreateEquipment(ctx, equipment)
}

// GetEquipment retrieves equipment by ID
func (uc *EquipmentUsecase) GetEquipment(ctx context.Context, id int64) (*entity.Equipment, error) {
	uc.log.WithContext(ctx).Infof("Getting equipment: %d", id)

	if id <= 0 {
		return nil, ErrInvalidEquipmentID
	}

	return uc.repo.GetEquipment(ctx, id)
}

// ListEquipment retrieves equipment with filtering and pagination
func (uc *EquipmentUsecase) ListEquipment(ctx context.Context, page, pageSize int32, customerID int64, equipmentType, status, healthStatus string) ([]*entity.Equipment, int32, error) {
	uc.log.WithContext(ctx).Infof("Listing equipment: page=%d, size=%d, customer=%d", page, pageSize, customerID)

	// Validate pagination parameters
	if page <= 0 {
		page = 1
	}
	if pageSize <= 0 || pageSize > 100 {
		pageSize = 20
	}

	return uc.repo.ListEquipment(ctx, page, pageSize, customerID, equipmentType, status, healthStatus)
}

// UpdateEquipment updates existing equipment
func (uc *EquipmentUsecase) UpdateEquipment(ctx context.Context, equipment *entity.Equipment) (*entity.Equipment, error) {
	uc.log.WithContext(ctx).Infof("Updating equipment: %d", equipment.ID)

	if equipment.ID <= 0 {
		return nil, ErrInvalidEquipmentID
	}

	// Update timestamp
	equipment.UpdatedAt = time.Now()

	return uc.repo.UpdateEquipment(ctx, equipment)
}

// DeleteEquipment deletes equipment by ID
func (uc *EquipmentUsecase) DeleteEquipment(ctx context.Context, id int64) error {
	uc.log.WithContext(ctx).Infof("Deleting equipment: %d", id)

	if id <= 0 {
		return ErrInvalidEquipmentID
	}

	return uc.repo.DeleteEquipment(ctx, id)
}

// ============================================================================
// HEALTH MONITORING
// ============================================================================

// GetEquipmentHealth retrieves equipment health data and trends
func (uc *EquipmentUsecase) GetEquipmentHealth(ctx context.Context, equipmentID int64) (*entity.Equipment, []*entity.HealthMetric, error) {
	uc.log.WithContext(ctx).Infof("Getting equipment health: %d", equipmentID)

	if equipmentID <= 0 {
		return nil, nil, ErrInvalidEquipmentID
	}

	return uc.repo.GetEquipmentHealth(ctx, equipmentID)
}

// UpdateEquipmentHealth updates equipment health metrics
func (uc *EquipmentUsecase) UpdateEquipmentHealth(ctx context.Context, equipmentID int64, metrics []*entity.HealthMetric) error {
	uc.log.WithContext(ctx).Infof("Updating equipment health: %d", equipmentID)

	if equipmentID <= 0 {
		return ErrInvalidEquipmentID
	}

	// Calculate overall health score based on metrics
	healthScore := uc.calculateHealthScore(metrics)

	// Update equipment health score
	equipment, err := uc.repo.GetEquipment(ctx, equipmentID)
	if err != nil {
		return err
	}

	equipment.HealthScore = healthScore
	equipment.HealthStatus = uc.getHealthStatus(healthScore)
	equipment.UpdatedAt = time.Now()

	_, err = uc.repo.UpdateEquipment(ctx, equipment)
	if err != nil {
		return err
	}

	return uc.repo.UpdateEquipmentHealth(ctx, equipmentID, metrics)
}

// GetHealthTrends retrieves health trends for specific metrics
func (uc *EquipmentUsecase) GetHealthTrends(ctx context.Context, equipmentID int64, metricName string, days int) ([]*entity.HealthMetric, error) {
	uc.log.WithContext(ctx).Infof("Getting health trends: equipment=%d, metric=%s, days=%d", equipmentID, metricName, days)

	if equipmentID <= 0 {
		return nil, ErrInvalidEquipmentID
	}

	if days <= 0 {
		days = 30 // Default to 30 days
	}

	return uc.repo.GetHealthTrends(ctx, equipmentID, metricName, days)
}

// ============================================================================
// MAINTENANCE MANAGEMENT
// ============================================================================

// ScheduleMaintenance schedules maintenance for equipment
func (uc *EquipmentUsecase) ScheduleMaintenance(ctx context.Context, schedule *entity.MaintenanceSchedule) (*entity.MaintenanceSchedule, error) {
	uc.log.WithContext(ctx).Infof("Scheduling maintenance for equipment: %d", schedule.EquipmentID)

	// Validation
	if schedule.EquipmentID <= 0 {
		return nil, ErrInvalidEquipmentID
	}
	if schedule.MaintenanceType == "" {
		return nil, ErrMaintenanceTypeRequired
	}
	if schedule.ScheduledDate.IsZero() {
		return nil, ErrMaintenanceDateRequired
	}

	// Set defaults
	if schedule.Status == "" {
		schedule.Status = "scheduled"
	}

	// Set timestamps
	now := time.Now()
	schedule.CreatedAt = now
	schedule.UpdatedAt = now

	return uc.repo.ScheduleMaintenance(ctx, schedule)
}

// GetMaintenanceHistory retrieves maintenance history for equipment
func (uc *EquipmentUsecase) GetMaintenanceHistory(ctx context.Context, equipmentID int64, page, pageSize int32, maintenanceType string) ([]*entity.MaintenanceRecord, int32, error) {
	uc.log.WithContext(ctx).Infof("Getting maintenance history: equipment=%d", equipmentID)

	if equipmentID <= 0 {
		return nil, 0, ErrInvalidEquipmentID
	}

	// Validate pagination
	if page <= 0 {
		page = 1
	}
	if pageSize <= 0 || pageSize > 100 {
		pageSize = 20
	}

	return uc.repo.GetMaintenanceHistory(ctx, equipmentID, page, pageSize, maintenanceType)
}

// ============================================================================
// PARTS MANAGEMENT
// ============================================================================

// GetEquipmentParts retrieves parts for equipment
func (uc *EquipmentUsecase) GetEquipmentParts(ctx context.Context, equipmentID int64) ([]*entity.EquipmentPart, error) {
	uc.log.WithContext(ctx).Infof("Getting equipment parts: %d", equipmentID)

	if equipmentID <= 0 {
		return nil, ErrInvalidEquipmentID
	}

	return uc.repo.GetEquipmentParts(ctx, equipmentID)
}

// UpdatePartStatus updates the status of an equipment part
func (uc *EquipmentUsecase) UpdatePartStatus(ctx context.Context, equipmentID, partID int64, status string, replacementDate *time.Time, notes string) (*entity.EquipmentPart, error) {
	uc.log.WithContext(ctx).Infof("Updating part status: equipment=%d, part=%d", equipmentID, partID)

	if equipmentID <= 0 {
		return nil, ErrInvalidEquipmentID
	}
	if partID <= 0 {
		return nil, ErrInvalidEquipmentID // Reusing error for simplicity
	}

	return uc.repo.UpdatePartStatus(ctx, equipmentID, partID, status, replacementDate, notes)
}

// ============================================================================
// ANALYTICS
// ============================================================================

// GetEquipmentAnalytics retrieves analytics data for equipment
func (uc *EquipmentUsecase) GetEquipmentAnalytics(ctx context.Context, equipmentID int64, startDate, endDate time.Time) (*entity.EquipmentAnalytics, error) {
	uc.log.WithContext(ctx).Infof("Getting equipment analytics: %d", equipmentID)

	if equipmentID <= 0 {
		return nil, ErrInvalidEquipmentID
	}

	return uc.repo.GetEquipmentAnalytics(ctx, equipmentID, startDate, endDate)
}

// GetFleetOverview retrieves fleet overview data
func (uc *EquipmentUsecase) GetFleetOverview(ctx context.Context, customerID int64, equipmentType string) (*entity.FleetOverview, error) {
	uc.log.WithContext(ctx).Info("Getting fleet overview")

	return uc.repo.GetFleetOverview(ctx, customerID, equipmentType)
}

// ============================================================================
// HELPER METHODS
// ============================================================================

// calculateHealthScore calculates overall health score from metrics
func (uc *EquipmentUsecase) calculateHealthScore(metrics []*entity.HealthMetric) float64 {
	if len(metrics) == 0 {
		return 1.0 // Default to perfect health if no metrics
	}

	totalScore := 0.0
	validMetrics := 0

	for _, metric := range metrics {
		if metric.ThresholdMin != nil && metric.ThresholdMax != nil {
			// Calculate score based on how close the value is to optimal range
			min := *metric.ThresholdMin
			max := *metric.ThresholdMax

			if metric.Value >= min && metric.Value <= max {
				totalScore += 1.0 // Perfect score for values in range
			} else {
				// Calculate degraded score based on distance from range
				if metric.Value < min {
					ratio := metric.Value / min
					totalScore += ratio
				} else {
					ratio := max / metric.Value
					totalScore += ratio
				}
			}
			validMetrics++
		}
	}

	if validMetrics == 0 {
		return 1.0
	}

	return totalScore / float64(validMetrics)
}

// getHealthStatus returns health status based on score
func (uc *EquipmentUsecase) getHealthStatus(score float64) string {
	switch {
	case score >= 0.9:
		return "excellent"
	case score >= 0.7:
		return "good"
	case score >= 0.5:
		return "fair"
	case score >= 0.3:
		return "poor"
	default:
		return "critical"
	}
}
