package biz

import (
	"context"
	"time"

	"gobackend-hvac-kratos/internal/entity"

	"github.com/go-kratos/kratos/v2/log"
)

// 🎫 Service Ticket Business Logic - Comprehensive Ticket Management
// GoBackend-Kratos HVAC CRM System

// ServiceTicketRepo defines the interface for service ticket data operations
type ServiceTicketRepo interface {
	// Ticket CRUD
	CreateTicket(ctx context.Context, ticket *entity.ServiceTicket) (*entity.ServiceTicket, error)
	GetTicket(ctx context.Context, id int64, includeRelations bool) (*entity.ServiceTicket, error)
	ListTickets(ctx context.Context, filters TicketFilters) ([]*entity.ServiceTicket, int32, error)
	UpdateTicket(ctx context.Context, ticket *entity.ServiceTicket) (*entity.ServiceTicket, error)
	DeleteTicket(ctx context.Context, id int64) error

	// Status Management
	UpdateTicketStatus(ctx context.Context, ticketID int64, newStatus entity.TicketStatus, changedBy int64, reason string) (*entity.ServiceTicket, *entity.TicketStatusHistory, error)
	GetTicketHistory(ctx context.Context, ticketID int64, page, pageSize int32) ([]*entity.TicketStatusHistory, int32, error)

	// Assignment Management
	AssignTicket(ctx context.Context, ticketID, technicianID int64, teamMemberIDs []int64, assignedBy int64) (*entity.ServiceTicket, error)
	GetTechnicianTickets(ctx context.Context, technicianID int64, filters TechnicianTicketFilters) ([]*entity.ServiceTicket, int32, error)

	// Communication
	AddComment(ctx context.Context, comment *entity.TicketComment) (*entity.TicketComment, error)
	GetComments(ctx context.Context, ticketID int64, page, pageSize int32, includeInternal bool) ([]*entity.TicketComment, int32, error)

	// Attachments
	AddAttachment(ctx context.Context, attachment *entity.TicketAttachment) (*entity.TicketAttachment, error)
	GetAttachments(ctx context.Context, ticketID int64) ([]*entity.TicketAttachment, error)

	// Resources
	AddResource(ctx context.Context, resource *entity.TicketResource) (*entity.TicketResource, error)
	GetResources(ctx context.Context, ticketID int64) ([]*entity.TicketResource, error)
	UpdateResourceUsage(ctx context.Context, ticketID, resourceID int64, quantityUsed int32, actualCost float64, notes string) (*entity.TicketResource, error)

	// Analytics
	GetTicketAnalytics(ctx context.Context, filters AnalyticsFilters) (*TicketAnalytics, error)
	GetTechnicianPerformance(ctx context.Context, technicianID int64, dateFrom, dateTo time.Time) (*TechnicianPerformance, error)
	GetSLAReport(ctx context.Context, dateFrom, dateTo time.Time, priority *entity.TicketPriority) (*SLAReport, error)

	// Customer Features
	RateService(ctx context.Context, ticketID int64, rating int32, feedback string, customerID int64) error
	GetCustomerTickets(ctx context.Context, customerID int64, filters CustomerTicketFilters) ([]*entity.ServiceTicket, int32, *CustomerTicketSummary, error)
}

// ServiceTicketUsecase encapsulates service ticket business logic
type ServiceTicketUsecase struct {
	repo ServiceTicketRepo
	log  *log.Helper
}

// NewServiceTicketUsecase creates a new service ticket use case
func NewServiceTicketUsecase(repo ServiceTicketRepo, logger log.Logger) *ServiceTicketUsecase {
	return &ServiceTicketUsecase{
		repo: repo,
		log:  log.NewHelper(logger),
	}
}

// ============================================================================
// TICKET CRUD OPERATIONS
// ============================================================================

// CreateTicket creates a new service ticket with validation
func (uc *ServiceTicketUsecase) CreateTicket(ctx context.Context, ticket *entity.ServiceTicket) (*entity.ServiceTicket, error) {
	uc.log.WithContext(ctx).Infof("Creating service ticket: %s", ticket.Title)

	// Business logic validation
	if ticket.Title == "" {
		return nil, ErrTicketTitleRequired
	}
	if ticket.CustomerID <= 0 {
		return nil, ErrInvalidCustomerID
	}
	if ticket.Type == "" {
		return nil, ErrTicketTypeRequired
	}

	// Set defaults
	if ticket.Status == "" {
		ticket.Status = entity.TicketStatusNew
	}
	if ticket.Priority == "" {
		ticket.Priority = entity.TicketPriorityNormal
	}
	if ticket.Urgency == "" {
		ticket.Urgency = entity.TicketUrgencyMedium
	}

	// Generate ticket number
	ticket.GenerateTicketNumber()

	// Calculate SLA deadline based on priority
	uc.calculateSLADeadline(ticket)

	// Set timestamps
	now := time.Now()
	ticket.CreatedAt = now
	ticket.UpdatedAt = now

	return uc.repo.CreateTicket(ctx, ticket)
}

// GetTicket retrieves a ticket by ID
func (uc *ServiceTicketUsecase) GetTicket(ctx context.Context, id int64, includeRelations bool) (*entity.ServiceTicket, error) {
	uc.log.WithContext(ctx).Infof("Getting service ticket: %d", id)

	if id <= 0 {
		return nil, ErrInvalidTicketID
	}

	return uc.repo.GetTicket(ctx, id, includeRelations)
}

// ListTickets retrieves tickets with filtering and pagination
func (uc *ServiceTicketUsecase) ListTickets(ctx context.Context, filters TicketFilters) ([]*entity.ServiceTicket, int32, error) {
	uc.log.WithContext(ctx).Infof("Listing service tickets with filters")

	// Validate pagination parameters
	if filters.Page <= 0 {
		filters.Page = 1
	}
	if filters.PageSize <= 0 || filters.PageSize > 100 {
		filters.PageSize = 20
	}

	return uc.repo.ListTickets(ctx, filters)
}

// UpdateTicket updates an existing ticket
func (uc *ServiceTicketUsecase) UpdateTicket(ctx context.Context, ticket *entity.ServiceTicket) (*entity.ServiceTicket, error) {
	uc.log.WithContext(ctx).Infof("Updating service ticket: %d", ticket.ID)

	if ticket.ID <= 0 {
		return nil, ErrInvalidTicketID
	}

	// Update timestamp
	ticket.UpdatedAt = time.Now()

	// Recalculate SLA if priority changed
	uc.calculateSLADeadline(ticket)

	return uc.repo.UpdateTicket(ctx, ticket)
}

// DeleteTicket deletes a ticket by ID
func (uc *ServiceTicketUsecase) DeleteTicket(ctx context.Context, id int64) error {
	uc.log.WithContext(ctx).Infof("Deleting service ticket: %d", id)

	if id <= 0 {
		return ErrInvalidTicketID
	}

	return uc.repo.DeleteTicket(ctx, id)
}

// ============================================================================
// STATUS MANAGEMENT
// ============================================================================

// UpdateTicketStatus updates ticket status with validation
func (uc *ServiceTicketUsecase) UpdateTicketStatus(ctx context.Context, ticketID int64, newStatus entity.TicketStatus, changedBy int64, reason string) (*entity.ServiceTicket, *entity.TicketStatusHistory, error) {
	uc.log.WithContext(ctx).Infof("Updating ticket status: %d to %s", ticketID, newStatus)

	if ticketID <= 0 {
		return nil, nil, ErrInvalidTicketID
	}

	// Get current ticket to validate transition
	ticket, err := uc.repo.GetTicket(ctx, ticketID, false)
	if err != nil {
		return nil, nil, err
	}

	// Validate status transition
	if !ticket.CanTransitionTo(newStatus) {
		return nil, nil, ErrInvalidStatusTransition
	}

	// Update timestamps based on status
	now := time.Now()
	if newStatus == entity.TicketStatusCompleted && ticket.CompletedAt == nil {
		ticket.CompletedAt = &now
		// Calculate actual duration
		if ticket.CreatedAt.IsZero() == false {
			duration := int32(now.Sub(ticket.CreatedAt).Minutes())
			ticket.ActualDurationMinutes = &duration
		}
	}
	if newStatus == entity.TicketStatusClosed && ticket.ClosedAt == nil {
		ticket.ClosedAt = &now
	}

	return uc.repo.UpdateTicketStatus(ctx, ticketID, newStatus, changedBy, reason)
}

// GetTicketHistory retrieves status change history
func (uc *ServiceTicketUsecase) GetTicketHistory(ctx context.Context, ticketID int64, page, pageSize int32) ([]*entity.TicketStatusHistory, int32, error) {
	uc.log.WithContext(ctx).Infof("Getting ticket history: %d", ticketID)

	if ticketID <= 0 {
		return nil, 0, ErrInvalidTicketID
	}

	// Validate pagination
	if page <= 0 {
		page = 1
	}
	if pageSize <= 0 || pageSize > 100 {
		pageSize = 20
	}

	return uc.repo.GetTicketHistory(ctx, ticketID, page, pageSize)
}

// ============================================================================
// ASSIGNMENT MANAGEMENT
// ============================================================================

// AssignTicket assigns a ticket to a technician
func (uc *ServiceTicketUsecase) AssignTicket(ctx context.Context, ticketID, technicianID int64, teamMemberIDs []int64, assignedBy int64) (*entity.ServiceTicket, error) {
	uc.log.WithContext(ctx).Infof("Assigning ticket %d to technician %d", ticketID, technicianID)

	if ticketID <= 0 {
		return nil, ErrInvalidTicketID
	}
	if technicianID <= 0 {
		return nil, ErrInvalidTechnicianID
	}

	// Get current ticket
	ticket, err := uc.repo.GetTicket(ctx, ticketID, false)
	if err != nil {
		return nil, err
	}

	// Validate ticket can be assigned
	if ticket.Status != entity.TicketStatusNew && ticket.Status != entity.TicketStatusAssigned {
		return nil, ErrTicketCannotBeAssigned
	}

	return uc.repo.AssignTicket(ctx, ticketID, technicianID, teamMemberIDs, assignedBy)
}

// GetTechnicianTickets retrieves tickets assigned to a technician
func (uc *ServiceTicketUsecase) GetTechnicianTickets(ctx context.Context, technicianID int64, filters TechnicianTicketFilters) ([]*entity.ServiceTicket, int32, error) {
	uc.log.WithContext(ctx).Infof("Getting technician tickets: %d", technicianID)

	if technicianID <= 0 {
		return nil, 0, ErrInvalidTechnicianID
	}

	// Validate pagination
	if filters.Page <= 0 {
		filters.Page = 1
	}
	if filters.PageSize <= 0 || filters.PageSize > 100 {
		filters.PageSize = 20
	}

	return uc.repo.GetTechnicianTickets(ctx, technicianID, filters)
}

// ============================================================================
// HELPER METHODS
// ============================================================================

// calculateSLADeadline calculates SLA deadline based on priority and urgency
func (uc *ServiceTicketUsecase) calculateSLADeadline(ticket *entity.ServiceTicket) {
	if ticket.SLADeadline != nil {
		return // Already set
	}

	var hours int
	switch ticket.Priority {
	case entity.TicketPriorityCritical:
		hours = 4 // 4 hours for critical
	case entity.TicketPriorityHigh:
		hours = 24 // 24 hours for high
	case entity.TicketPriorityNormal:
		hours = 72 // 72 hours for normal
	case entity.TicketPriorityLow:
		hours = 168 // 1 week for low
	default:
		hours = 72
	}

	// Adjust based on urgency
	switch ticket.Urgency {
	case entity.TicketUrgencyEmergency:
		hours = hours / 4 // Quarter the time for emergency
	case entity.TicketUrgencyHigh:
		hours = hours / 2 // Half the time for high urgency
	}

	deadline := time.Now().Add(time.Duration(hours) * time.Hour)
	ticket.SLADeadline = &deadline
}

// ============================================================================
// FILTER AND ANALYTICS STRUCTURES
// ============================================================================

// TicketFilters represents filters for listing tickets
type TicketFilters struct {
	Page         int32                 `json:"page"`
	PageSize     int32                 `json:"page_size"`
	CustomerID   int64                 `json:"customer_id"`
	TechnicianID int64                 `json:"technician_id"`
	Status       entity.TicketStatus   `json:"status"`
	Priority     entity.TicketPriority `json:"priority"`
	Type         entity.TicketType     `json:"type"`
	DateFrom     *time.Time            `json:"date_from"`
	DateTo       *time.Time            `json:"date_to"`
	SearchQuery  string                `json:"search_query"`
	Tags         []string              `json:"tags"`
	SortBy       string                `json:"sort_by"`
	SortOrder    string                `json:"sort_order"`
}

// TechnicianTicketFilters represents filters for technician tickets
type TechnicianTicketFilters struct {
	Page     int32               `json:"page"`
	PageSize int32               `json:"page_size"`
	Status   entity.TicketStatus `json:"status"`
	DateFrom *time.Time          `json:"date_from"`
	DateTo   *time.Time          `json:"date_to"`
}

// CustomerTicketFilters represents filters for customer tickets
type CustomerTicketFilters struct {
	Page     int32               `json:"page"`
	PageSize int32               `json:"page_size"`
	Status   entity.TicketStatus `json:"status"`
	DateFrom *time.Time          `json:"date_from"`
	DateTo   *time.Time          `json:"date_to"`
}

// AnalyticsFilters represents filters for analytics
type AnalyticsFilters struct {
	DateFrom     time.Time          `json:"date_from"`
	DateTo       time.Time          `json:"date_to"`
	TechnicianID *int64             `json:"technician_id"`
	CustomerID   *int64             `json:"customer_id"`
	Type         *entity.TicketType `json:"type"`
}

// TicketAnalytics represents comprehensive ticket analytics
type TicketAnalytics struct {
	TotalTickets               int32                     `json:"total_tickets"`
	CompletedTickets           int32                     `json:"completed_tickets"`
	OverdueTickets             int32                     `json:"overdue_tickets"`
	CompletionRate             float64                   `json:"completion_rate"`
	AverageResolutionTimeHours float64                   `json:"average_resolution_time_hours"`
	AverageResponseTimeHours   float64                   `json:"average_response_time_hours"`
	SLAComplianceRate          float64                   `json:"sla_compliance_rate"`
	TotalRevenue               float64                   `json:"total_revenue"`
	AverageTicketValue         float64                   `json:"average_ticket_value"`
	TypeStats                  []TicketTypeStats         `json:"type_stats"`
	TechnicianStats            []TechnicianStats         `json:"technician_stats"`
	SatisfactionStats          CustomerSatisfactionStats `json:"satisfaction_stats"`
}

// TicketTypeStats represents statistics by ticket type
type TicketTypeStats struct {
	Type                 entity.TicketType `json:"type"`
	Count                int32             `json:"count"`
	AverageDurationHours float64           `json:"average_duration_hours"`
	AverageCost          float64           `json:"average_cost"`
}

// TechnicianStats represents technician performance statistics
type TechnicianStats struct {
	TechnicianID               int64   `json:"technician_id"`
	TechnicianName             string  `json:"technician_name"`
	TicketsCompleted           int32   `json:"tickets_completed"`
	AverageResolutionTimeHours float64 `json:"average_resolution_time_hours"`
	CustomerRatingAverage      float64 `json:"customer_rating_average"`
	UtilizationRate            float64 `json:"utilization_rate"`
}

// CustomerSatisfactionStats represents customer satisfaction metrics
type CustomerSatisfactionStats struct {
	AverageRating  float64 `json:"average_rating"`
	TotalRatings   int32   `json:"total_ratings"`
	FiveStarCount  int32   `json:"five_star_count"`
	FourStarCount  int32   `json:"four_star_count"`
	ThreeStarCount int32   `json:"three_star_count"`
	TwoStarCount   int32   `json:"two_star_count"`
	OneStarCount   int32   `json:"one_star_count"`
}

// TechnicianPerformance represents detailed technician performance
type TechnicianPerformance struct {
	TechnicianID               int64    `json:"technician_id"`
	TechnicianName             string   `json:"technician_name"`
	TicketsAssigned            int32    `json:"tickets_assigned"`
	TicketsCompleted           int32    `json:"tickets_completed"`
	TicketsOverdue             int32    `json:"tickets_overdue"`
	CompletionRate             float64  `json:"completion_rate"`
	AverageResolutionTimeHours float64  `json:"average_resolution_time_hours"`
	CustomerRatingAverage      float64  `json:"customer_rating_average"`
	RevenueGenerated           float64  `json:"revenue_generated"`
	HoursWorked                int32    `json:"hours_worked"`
	EfficiencyScore            float64  `json:"efficiency_score"`
	Strengths                  []string `json:"strengths"`
	ImprovementAreas           []string `json:"improvement_areas"`
}

// SLAReport represents SLA compliance report
type SLAReport struct {
	TotalTickets      int32              `json:"total_tickets"`
	SLAMet            int32              `json:"sla_met"`
	SLABreached       int32              `json:"sla_breached"`
	SLAComplianceRate float64            `json:"sla_compliance_rate"`
	Breaches          []SLABreachDetail  `json:"breaches"`
	PriorityStats     []PrioritySLAStats `json:"priority_stats"`
}

// SLABreachDetail represents details of an SLA breach
type SLABreachDetail struct {
	TicketID            int64                 `json:"ticket_id"`
	TicketNumber        string                `json:"ticket_number"`
	Priority            entity.TicketPriority `json:"priority"`
	SLADeadline         time.Time             `json:"sla_deadline"`
	ActualCompletion    time.Time             `json:"actual_completion"`
	BreachDurationHours int32                 `json:"breach_duration_hours"`
	BreachReason        string                `json:"breach_reason"`
}

// PrioritySLAStats represents SLA statistics by priority
type PrioritySLAStats struct {
	Priority                 entity.TicketPriority `json:"priority"`
	TotalTickets             int32                 `json:"total_tickets"`
	SLAMet                   int32                 `json:"sla_met"`
	ComplianceRate           float64               `json:"compliance_rate"`
	TargetResponseTimeHours  int32                 `json:"target_response_time_hours"`
	AverageResponseTimeHours float64               `json:"average_response_time_hours"`
}

// CustomerTicketSummary represents summary of customer tickets
type CustomerTicketSummary struct {
	CustomerID           int64      `json:"customer_id"`
	TotalTickets         int32      `json:"total_tickets"`
	OpenTickets          int32      `json:"open_tickets"`
	CompletedTickets     int32      `json:"completed_tickets"`
	AverageRating        float64    `json:"average_rating"`
	TotalSpent           float64    `json:"total_spent"`
	LastServiceDate      *time.Time `json:"last_service_date"`
	NextScheduledService *time.Time `json:"next_scheduled_service"`
}
