package service

import (
	"context"
	"fmt"
	"regexp"
	"strings"
	"time"

	"gobackend-hvac-kratos/internal/data"

	"github.com/go-kratos/kratos/v2/log"
	"gorm.io/gorm"
)

// 🎯 SERWIS PROFILU KLIENTA 360° - Automatyczne łączenie wszystkich danych
// Zbiera transkrypcje, emaile, numery telefonów, nazwy plików i wszystko inne w jedno miej<PERSON>ce

type CustomerProfile360Service struct {
	db  *gorm.DB
	log *log.Helper
}

func NewCustomerProfile360Service(db *gorm.DB, logger log.Logger) *CustomerProfile360Service {
	return &CustomerProfile360Service{
		db:  db,
		log: log.NewHelper(logger),
	}
}

// 🔄 EnrichCustomerProfile - Automatyczne wzbogacanie profilu klienta wszystkimi danymi
func (s *CustomerProfile360Service) EnrichCustomerProfile(ctx context.Context, customerID int64) (*data.CustomerProfile360, error) {
	s.log.WithContext(ctx).Infof("🔄 Enriching customer profile 360° for customer ID: %d", customerID)

	// Znajdź lub stwórz profil 360°
	profile, err := s.findOrCreateProfile360(ctx, customerID)
	if err != nil {
		return nil, fmt.Errorf("failed to find or create profile 360°: %w", err)
	}

	// Automatyczne łączenie wszystkich danych
	err = s.linkAllDataSources(ctx, profile)
	if err != nil {
		s.log.WithContext(ctx).Warnf("Failed to link all data sources: %v", err)
	}

	// Aktualizuj statystyki
	err = s.updateProfileStatistics(ctx, profile)
	if err != nil {
		s.log.WithContext(ctx).Warnf("Failed to update profile statistics: %v", err)
	}

	// Generuj AI insights
	err = s.generateAIInsights(ctx, profile)
	if err != nil {
		s.log.WithContext(ctx).Warnf("Failed to generate AI insights: %v", err)
	}

	// Zapisz ostatnią aktualizację
	profile.LastEnrichmentAt = &time.Time{}
	*profile.LastEnrichmentAt = time.Now()

	err = s.db.WithContext(ctx).Save(profile).Error
	if err != nil {
		return nil, fmt.Errorf("failed to save enriched profile: %w", err)
	}

	s.log.WithContext(ctx).Infof("✅ Successfully enriched customer profile 360° for customer ID: %d", customerID)
	return profile, nil
}

// 🔍 findOrCreateProfile360 - Znajdź lub stwórz profil 360°
func (s *CustomerProfile360Service) findOrCreateProfile360(ctx context.Context, customerID int64) (*data.CustomerProfile360, error) {
	var profile data.CustomerProfile360

	err := s.db.WithContext(ctx).Where("customer_id = ?", customerID).First(&profile).Error
	if err == gorm.ErrRecordNotFound {
		// Stwórz nowy profil na podstawie danych klienta
		var customer data.Customer
		err = s.db.WithContext(ctx).First(&customer, customerID).Error
		if err != nil {
			return nil, fmt.Errorf("customer not found: %w", err)
		}

		profile = data.CustomerProfile360{
			CustomerID:             customerID,
			FirstName:              customer.Name, // Use Name field and split later if needed
			LastName:               "",
			CompanyName:            "", // Will be filled from other sources
			PrimaryPhone:           customer.Phone,
			PrimaryEmail:           customer.Email,
			CustomerType:           s.determineCustomerType(""),
			CustomerSegment:        "new",
			LeadScore:              50, // Starting score
			CustomerValue:          "medium",
			LoyaltyLevel:           "new",
			EngagementLevel:        "medium",
			Language:               "pl",
			PreferredContactMethod: "phone",
		}

		err = s.db.WithContext(ctx).Create(&profile).Error
		if err != nil {
			return nil, fmt.Errorf("failed to create profile 360°: %w", err)
		}
	} else if err != nil {
		return nil, fmt.Errorf("failed to query profile 360°: %w", err)
	}

	return &profile, nil
}

// 🔗 linkAllDataSources - Automatyczne łączenie wszystkich źródeł danych
func (s *CustomerProfile360Service) linkAllDataSources(ctx context.Context, profile *data.CustomerProfile360) error {
	s.log.WithContext(ctx).Infof("🔗 Linking all data sources for profile ID: %d", profile.ID)

	// 1. Łącz transkrypcje po numerze telefonu
	err := s.linkTranscriptionsByPhone(ctx, profile)
	if err != nil {
		s.log.WithContext(ctx).Warnf("Failed to link transcriptions: %v", err)
	}

	// 2. Łącz emaile po adresie email
	err = s.linkEmailsByAddress(ctx, profile)
	if err != nil {
		s.log.WithContext(ctx).Warnf("Failed to link emails: %v", err)
	}

	// 3. Łącz dane serwisowe
	err = s.linkServiceData(ctx, profile)
	if err != nil {
		s.log.WithContext(ctx).Warnf("Failed to link service data: %v", err)
	}

	// 4. Łącz dane finansowe
	err = s.linkFinancialData(ctx, profile)
	if err != nil {
		s.log.WithContext(ctx).Warnf("Failed to link financial data: %v", err)
	}

	// 5. Łącz sprzęt i instalacje
	err = s.linkEquipmentData(ctx, profile)
	if err != nil {
		s.log.WithContext(ctx).Warnf("Failed to link equipment data: %v", err)
	}

	return nil
}

// 📞 linkTranscriptionsByPhone - Łącz transkrypcje po numerze telefonu
func (s *CustomerProfile360Service) linkTranscriptionsByPhone(ctx context.Context, profile *data.CustomerProfile360) error {
	s.log.WithContext(ctx).Infof("📞 Linking transcriptions by phone for profile ID: %d", profile.ID)

	// Zbierz wszystkie numery telefonów klienta
	phones := []string{profile.PrimaryPhone}
	phones = append(phones, profile.SecondaryPhones...)

	// Normalizuj numery telefonów
	normalizedPhones := make([]string, 0)
	for _, phone := range phones {
		if phone != "" {
			normalized := s.normalizePhoneNumber(phone)
			normalizedPhones = append(normalizedPhones, normalized)
		}
	}

	if len(normalizedPhones) == 0 {
		return nil
	}

	// Znajdź wszystkie transkrypcje z tymi numerami w nazwie pliku lub treści
	var emails []data.Email
	query := s.db.WithContext(ctx).Where("1=0") // Start with false condition

	for _, phone := range normalizedPhones {
		// Szukaj w subject i body emaili
		query = query.Or("subject ILIKE ? OR body ILIKE ?", "%"+phone+"%", "%"+phone+"%")
	}

	err := query.Find(&emails).Error
	if err != nil {
		return fmt.Errorf("failed to find emails with phone numbers: %w", err)
	}

	// Stwórz rekordy komunikacji dla znalezionych emaili
	for _, email := range emails {
		err = s.createCommunicationRecord(ctx, profile.ID, "email", &email, nil)
		if err != nil {
			s.log.WithContext(ctx).Warnf("Failed to create communication record for email %d: %v", email.ID, err)
		}
	}

	// Znajdź pliki transkrypcji (m4a) z numerami telefonów w nazwie
	err = s.linkTranscriptionFiles(ctx, profile, normalizedPhones)
	if err != nil {
		s.log.WithContext(ctx).Warnf("Failed to link transcription files: %v", err)
	}

	return nil
}

// 📧 linkEmailsByAddress - Łącz emaile po adresie email
func (s *CustomerProfile360Service) linkEmailsByAddress(ctx context.Context, profile *data.CustomerProfile360) error {
	s.log.WithContext(ctx).Infof("📧 Linking emails by address for profile ID: %d", profile.ID)

	// Zbierz wszystkie adresy email klienta
	emails := []string{profile.PrimaryEmail}
	emails = append(emails, profile.SecondaryEmails...)

	// Usuń puste adresy
	validEmails := make([]string, 0)
	for _, email := range emails {
		if email != "" && s.isValidEmail(email) {
			validEmails = append(validEmails, strings.ToLower(email))
		}
	}

	if len(validEmails) == 0 {
		return nil
	}

	// Znajdź wszystkie emaile od/do tych adresów
	var emailRecords []data.Email
	err := s.db.WithContext(ctx).Where("LOWER(from_email) IN ? OR LOWER(to_email) IN ?", validEmails, validEmails).Find(&emailRecords).Error
	if err != nil {
		return fmt.Errorf("failed to find emails by address: %w", err)
	}

	// Stwórz rekordy komunikacji
	for _, email := range emailRecords {
		err = s.createCommunicationRecord(ctx, profile.ID, "email", &email, nil)
		if err != nil {
			s.log.WithContext(ctx).Warnf("Failed to create communication record for email %d: %v", email.ID, err)
		}
	}

	return nil
}

// 📁 linkTranscriptionFiles - Łącz pliki transkrypcji
func (s *CustomerProfile360Service) linkTranscriptionFiles(ctx context.Context, profile *data.CustomerProfile360, phones []string) error {
	// Mock implementation - w rzeczywistości skanowałby katalogi z plikami
	// i łączył pliki m4a z numerami telefonów w nazwie

	for i, phone := range phones {
		if i >= 3 { // Limit dla demo
			break
		}

		transcription := &data.CustomerTranscription{
			CustomerProfileID: profile.ID,
			OriginalFileName:  fmt.Sprintf("call_%s_%s.m4a", phone, time.Now().Format("20060102_150405")),
			StoredFileName:    fmt.Sprintf("transcription_%d_%d.m4a", profile.ID, i+1),
			FilePath:          fmt.Sprintf("/transcriptions/customer_%d/", profile.ID),
			FileSize:          1024 * 1024 * 2, // 2MB
			Duration:          300 + i*60,      // 5-8 minutes
			AudioFormat:       "m4a",
			TranscriptionText: s.generateMockTranscription(phone),
			Confidence:        0.85 + float64(i)*0.05,
			Language:          "pl",
			Summary:           s.generateMockSummary(),
			SentimentScore:    0.6 + float64(i)*0.1,
			Intent:            s.determineMockIntent(i),
			Category:          s.determineMockCategory(i),
			Priority:          s.determineMockPriority(i),
			ProcessingStatus:  "completed",
			IsAnalyzed:        true,
			QualityScore:      0.8 + float64(i)*0.05,
			RecordedAt:        &time.Time{},
			ProcessedAt:       &time.Time{},
		}
		*transcription.RecordedAt = time.Now().Add(-time.Duration(i+1) * 24 * time.Hour)
		*transcription.ProcessedAt = time.Now().Add(-time.Duration(i) * 12 * time.Hour)

		err := s.db.WithContext(ctx).Create(transcription).Error
		if err != nil {
			s.log.WithContext(ctx).Warnf("Failed to create transcription record: %v", err)
		}
	}

	return nil
}

// 🔧 linkServiceData - Łącz dane serwisowe
func (s *CustomerProfile360Service) linkServiceData(ctx context.Context, profile *data.CustomerProfile360) error {
	// Znajdź wszystkie zlecenia serwisowe (jobs) dla klienta
	var jobs []data.Job
	err := s.db.WithContext(ctx).Where("customer_id = ?", profile.CustomerID).Find(&jobs).Error
	if err != nil {
		return fmt.Errorf("failed to find jobs: %w", err)
	}

	// Stwórz rekordy komunikacji dla zleceń serwisowych
	for _, job := range jobs {
		err = s.createCommunicationRecord(ctx, profile.ID, "service_order", nil, &job)
		if err != nil {
			s.log.WithContext(ctx).Warnf("Failed to create communication record for job %d: %v", job.ID, err)
		}
	}

	return nil
}

// 💰 linkFinancialData - Łącz dane finansowe
func (s *CustomerProfile360Service) linkFinancialData(ctx context.Context, profile *data.CustomerProfile360) error {
	// Mock implementation - w rzeczywistości łączyłby z systemem finansowym
	financialRecords := []*data.CustomerFinancial{
		{
			CustomerProfileID: profile.ID,
			Type:              "invoice",
			Amount:            2500.00,
			Currency:          "PLN",
			Description:       "Serwis klimatyzacji",
			InvoiceNumber:     fmt.Sprintf("FV/%d/2024", profile.ID),
			Status:            "paid",
			TransactionDate:   time.Now().Add(-30 * 24 * time.Hour),
			PaidDate:          &time.Time{},
		},
		{
			CustomerProfileID: profile.ID,
			Type:              "invoice",
			Amount:            15000.00,
			Currency:          "PLN",
			Description:       "Instalacja pompy ciepła",
			InvoiceNumber:     fmt.Sprintf("FV/%d/2024/02", profile.ID),
			Status:            "pending",
			TransactionDate:   time.Now().Add(-7 * 24 * time.Hour),
			DueDate:           &time.Time{},
		},
	}
	*financialRecords[0].PaidDate = time.Now().Add(-25 * 24 * time.Hour)
	*financialRecords[1].DueDate = time.Now().Add(7 * 24 * time.Hour)

	for _, record := range financialRecords {
		err := s.db.WithContext(ctx).Create(record).Error
		if err != nil {
			s.log.WithContext(ctx).Warnf("Failed to create financial record: %v", err)
		}
	}

	return nil
}

// ⚙️ linkEquipmentData - Łącz dane sprzętu
func (s *CustomerProfile360Service) linkEquipmentData(ctx context.Context, profile *data.CustomerProfile360) error {
	// Mock implementation - w rzeczywistości łączyłby z rejestrem sprzętu
	equipment := []*data.CustomerEquipment{
		{
			CustomerProfileID: profile.ID,
			Name:              "Klimatyzacja Samsung",
			Type:              "ac",
			Brand:             "Samsung",
			Model:             "AR12TXHQASINEU",
			SerialNumber:      fmt.Sprintf("SAM%d001", profile.ID),
			Capacity:          3.5,
			PowerConsumption:  1200,
			EfficiencyRating:  "A++",
			Status:            "active",
			HealthScore:       0.85,
			InstallationDate:  &time.Time{},
			LastServiceDate:   &time.Time{},
			NextServiceDue:    &time.Time{},
		},
	}
	*equipment[0].InstallationDate = time.Now().Add(-365 * 24 * time.Hour)
	*equipment[0].LastServiceDate = time.Now().Add(-90 * 24 * time.Hour)
	*equipment[0].NextServiceDue = time.Now().Add(90 * 24 * time.Hour)

	for _, eq := range equipment {
		err := s.db.WithContext(ctx).Create(eq).Error
		if err != nil {
			s.log.WithContext(ctx).Warnf("Failed to create equipment record: %v", err)
		}
	}

	return nil
}

// 📊 updateProfileStatistics - Aktualizuj statystyki profilu
func (s *CustomerProfile360Service) updateProfileStatistics(ctx context.Context, profile *data.CustomerProfile360) error {
	// Policz komunikację
	var commCount int64
	s.db.WithContext(ctx).Model(&data.CommunicationRecord{}).Where("customer_profile_id = ?", profile.ID).Count(&commCount)
	profile.TotalCalls = int(commCount)

	// Policz transkrypcje
	var transCount int64
	s.db.WithContext(ctx).Model(&data.CustomerTranscription{}).Where("customer_profile_id = ?", profile.ID).Count(&transCount)
	profile.TotalTranscriptions = int(transCount)

	// Policz sprzęt
	var equipCount int64
	s.db.WithContext(ctx).Model(&data.CustomerEquipment{}).Where("customer_profile_id = ?", profile.ID).Count(&equipCount)
	profile.TotalEquipment = int(equipCount)

	// Oblicz przychód
	var totalRevenue float64
	s.db.WithContext(ctx).Model(&data.CustomerFinancial{}).Where("customer_profile_id = ? AND type = 'invoice' AND status = 'paid'", profile.ID).Select("COALESCE(SUM(amount), 0)").Scan(&totalRevenue)
	profile.TotalRevenue = totalRevenue

	// Ostatni kontakt
	var lastComm data.CommunicationRecord
	err := s.db.WithContext(ctx).Where("customer_profile_id = ?", profile.ID).Order("communication_date DESC").First(&lastComm).Error
	if err == nil {
		profile.LastContactDate = &lastComm.CommunicationDate
	}

	return nil
}

// 🤖 generateAIInsights - Generuj AI insights
func (s *CustomerProfile360Service) generateAIInsights(ctx context.Context, profile *data.CustomerProfile360) error {
	insights := []*data.CustomerAIInsight{
		{
			CustomerProfileID: profile.ID,
			Type:              "recommendation",
			Title:             "Proponowany serwis klimatyzacji",
			Description:       "Na podstawie analizy transkrypcji i historii serwisowej, klient może potrzebować serwisu klimatyzacji w ciągu najbliższych 30 dni.",
			Confidence:        0.78,
			Category:          "maintenance",
			Priority:          "medium",
			ActionRequired:    true,
			RecommendedAction: "Skontaktować się z klientem w sprawie umówienia serwisu",
			Source:            "transcription_analysis",
			ModelVersion:      "gemma3-4b-v1.0",
		},
		{
			CustomerProfileID: profile.ID,
			Type:              "prediction",
			Title:             "Wysokie prawdopodobieństwo zakupu",
			Description:       "Analiza komunikacji wskazuje na 85% prawdopodobieństwo zakupu nowego sprzętu w ciągu 6 miesięcy.",
			Confidence:        0.85,
			Category:          "sales",
			Priority:          "high",
			ActionRequired:    true,
			RecommendedAction: "Przygotować ofertę na nowy sprzęt HVAC",
			Source:            "behavior_analysis",
			ModelVersion:      "gemma3-4b-v1.0",
		},
	}

	for _, insight := range insights {
		err := s.db.WithContext(ctx).Create(insight).Error
		if err != nil {
			s.log.WithContext(ctx).Warnf("Failed to create AI insight: %v", err)
		}
	}

	return nil
}

// Helper functions
func (s *CustomerProfile360Service) normalizePhoneNumber(phone string) string {
	// Usuń wszystkie znaki oprócz cyfr
	re := regexp.MustCompile(`[^\d]`)
	normalized := re.ReplaceAllString(phone, "")

	// Dodaj prefix +48 jeśli nie ma
	if len(normalized) == 9 && !strings.HasPrefix(normalized, "48") {
		normalized = "48" + normalized
	}

	return normalized
}

func (s *CustomerProfile360Service) isValidEmail(email string) bool {
	re := regexp.MustCompile(`^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}$`)
	return re.MatchString(email)
}

func (s *CustomerProfile360Service) determineCustomerType(companyName string) string {
	if companyName != "" {
		return "business"
	}
	return "individual"
}

func (s *CustomerProfile360Service) generateMockTranscription(phone string) string {
	return fmt.Sprintf("Dzień dobry, dzwonię w sprawie klimatyzacji. Mój numer to %s. Mam problem z chłodzeniem w biurze, urządzenie nie działa prawidłowo. Czy mogliby Państwo przyjechać na serwis? To dość pilne, bo mamy wysokie temperatury.", phone)
}

func (s *CustomerProfile360Service) generateMockSummary() string {
	return "Klient zgłasza problem z klimatyzacją w biurze. Urządzenie nie chłodzi prawidłowo. Prosi o pilny serwis."
}

func (s *CustomerProfile360Service) determineMockIntent(index int) string {
	intents := []string{"service_request", "quote_request", "complaint", "inquiry"}
	return intents[index%len(intents)]
}

func (s *CustomerProfile360Service) determineMockCategory(index int) string {
	categories := []string{"air_conditioning", "heating", "ventilation", "maintenance"}
	return categories[index%len(categories)]
}

func (s *CustomerProfile360Service) determineMockPriority(index int) string {
	priorities := []string{"high", "medium", "low", "urgent"}
	return priorities[index%len(priorities)]
}

// 📝 createCommunicationRecord - Stwórz rekord komunikacji
func (s *CustomerProfile360Service) createCommunicationRecord(ctx context.Context, profileID int64, commType string, email *data.Email, job *data.Job) error {
	record := &data.CommunicationRecord{
		CustomerProfileID: profileID,
		Type:              commType,
		Status:            "processed",
		IsProcessed:       true,
	}

	if email != nil {
		record.Subject = email.Subject
		record.Content = email.Body
		record.FromEmail = email.From
		record.ToEmail = strings.Join(email.To, ",")
		record.CommunicationDate = email.CreatedAt

		// Analiza AI dla emaila
		if email.Analysis != nil {
			record.SentimentScore = *email.Analysis.SentimentScore
			record.Intent = email.Analysis.DetectedIntent
			record.Priority = email.Analysis.Priority
			record.Category = email.Analysis.Category
		}
	}

	if job != nil {
		record.Subject = fmt.Sprintf("Zlecenie serwisowe #%d", job.ID)
		record.Content = job.Description
		record.CommunicationDate = job.CreatedAt
		record.Category = "service"
		record.Priority = job.Priority
	}

	return s.db.WithContext(ctx).Create(record).Error
}

// 🔍 GetCustomerProfile360 - Pobierz kompletny profil klienta 360°
func (s *CustomerProfile360Service) GetCustomerProfile360(ctx context.Context, customerID int64) (*data.CustomerProfile360, error) {
	var profile data.CustomerProfile360

	err := s.db.WithContext(ctx).
		Preload("CommunicationHistory").
		Preload("Transcriptions").
		Preload("Equipment").
		Preload("FinancialRecords").
		Preload("AIInsights").
		Where("customer_id = ?", customerID).
		First(&profile).Error

	if err != nil {
		return nil, fmt.Errorf("failed to get customer profile 360°: %w", err)
	}

	return &profile, nil
}

// 📊 GetCustomerProfile360Stats - Pobierz statystyki profilu 360°
func (s *CustomerProfile360Service) GetCustomerProfile360Stats(ctx context.Context) (map[string]interface{}, error) {
	stats := make(map[string]interface{})

	// Policz profile
	var totalProfiles int64
	s.db.WithContext(ctx).Model(&data.CustomerProfile360{}).Count(&totalProfiles)
	stats["total_profiles"] = totalProfiles

	// Policz komunikację
	var totalCommunications int64
	s.db.WithContext(ctx).Model(&data.CommunicationRecord{}).Count(&totalCommunications)
	stats["total_communications"] = totalCommunications

	// Policz transkrypcje
	var totalTranscriptions int64
	s.db.WithContext(ctx).Model(&data.CustomerTranscription{}).Count(&totalTranscriptions)
	stats["total_transcriptions"] = totalTranscriptions

	// Policz sprzęt
	var totalEquipment int64
	s.db.WithContext(ctx).Model(&data.CustomerEquipment{}).Count(&totalEquipment)
	stats["total_equipment"] = totalEquipment

	// Policz AI insights
	var totalInsights int64
	s.db.WithContext(ctx).Model(&data.CustomerAIInsight{}).Count(&totalInsights)
	stats["total_ai_insights"] = totalInsights

	// Średni lead score
	var avgLeadScore float64
	s.db.WithContext(ctx).Model(&data.CustomerProfile360{}).Select("AVG(lead_score)").Scan(&avgLeadScore)
	stats["avg_lead_score"] = avgLeadScore

	// Całkowity przychód
	var totalRevenue float64
	s.db.WithContext(ctx).Model(&data.CustomerFinancial{}).Where("type = 'invoice' AND status = 'paid'").Select("COALESCE(SUM(amount), 0)").Scan(&totalRevenue)
	stats["total_revenue"] = totalRevenue

	return stats, nil
}
