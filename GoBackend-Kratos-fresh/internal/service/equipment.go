package service

import (
	"context"
	"time"

	"github.com/go-kratos/kratos/v2/log"
	"google.golang.org/protobuf/types/known/timestamppb"

	pb "gobackend-hvac-kratos/api/equipment/v1"
	"gobackend-hvac-kratos/internal/biz"
	"gobackend-hvac-kratos/internal/entity"
)

// 🏭 Equipment Service - HVAC Equipment Management
// GoBackend-Kratos HVAC CRM System

// EquipmentService implements the equipment service
type EquipmentService struct {
	pb.UnimplementedEquipmentServiceServer

	equipmentUc *biz.EquipmentUsecase
	log         *log.Helper
}

// NewEquipmentService creates a new equipment service
func NewEquipmentService(equipmentUc *biz.EquipmentUsecase, logger log.Logger) *EquipmentService {
	return &EquipmentService{
		equipmentUc: equipmentUc,
		log:         log.NewHelper(logger),
	}
}

// ============================================================================
// EQUIPMENT CRUD OPERATIONS
// ============================================================================

// CreateEquipment creates a new equipment
func (s *EquipmentService) CreateEquipment(ctx context.Context, req *pb.CreateEquipmentRequest) (*pb.CreateEquipmentResponse, error) {
	s.log.WithContext(ctx).Infof("Creating equipment: %s", req.Name)

	equipment := &entity.Equipment{
		CustomerID:     req.CustomerId,
		Name:           req.Name,
		Type:           req.Type,
		Brand:          req.Brand,
		Model:          req.Model,
		SerialNumber:   req.SerialNumber,
		Location:       req.Location,
		Specifications: convertStructToJSON(req.Specifications),
		Metadata:       convertStructToJSON(req.Metadata),
	}

	// Handle optional fields
	if req.InstallationDate != nil {
		installDate := req.InstallationDate.AsTime()
		equipment.InstallationDate = &installDate
	}
	if req.WarrantyExpiry != nil {
		warrantyDate := req.WarrantyExpiry.AsTime()
		equipment.WarrantyExpiry = &warrantyDate
	}
	if req.Latitude != 0 {
		equipment.Latitude = &req.Latitude
	}
	if req.Longitude != 0 {
		equipment.Longitude = &req.Longitude
	}

	result, err := s.equipmentUc.CreateEquipment(ctx, equipment)
	if err != nil {
		return nil, err
	}

	return &pb.CreateEquipmentResponse{
		Equipment: convertEquipmentToProto(result),
	}, nil
}

// GetEquipment retrieves equipment by ID
func (s *EquipmentService) GetEquipment(ctx context.Context, req *pb.GetEquipmentRequest) (*pb.GetEquipmentResponse, error) {
	s.log.WithContext(ctx).Infof("Getting equipment: %d", req.Id)

	equipment, err := s.equipmentUc.GetEquipment(ctx, req.Id)
	if err != nil {
		return nil, err
	}

	return &pb.GetEquipmentResponse{
		Equipment: convertEquipmentToProto(equipment),
	}, nil
}

// ListEquipment retrieves equipment with filtering and pagination
func (s *EquipmentService) ListEquipment(ctx context.Context, req *pb.ListEquipmentRequest) (*pb.ListEquipmentResponse, error) {
	s.log.WithContext(ctx).Infof("Listing equipment: page=%d, size=%d", req.Page, req.PageSize)

	equipment, total, err := s.equipmentUc.ListEquipment(ctx, req.Page, req.PageSize, req.CustomerId, req.Type, req.Status, req.HealthStatus)
	if err != nil {
		return nil, err
	}

	pbEquipment := make([]*pb.Equipment, len(equipment))
	for i, eq := range equipment {
		pbEquipment[i] = convertEquipmentToProto(eq)
	}

	return &pb.ListEquipmentResponse{
		Equipment:  pbEquipment,
		TotalCount: total,
		Page:       req.Page,
		PageSize:   req.PageSize,
	}, nil
}

// UpdateEquipment updates existing equipment
func (s *EquipmentService) UpdateEquipment(ctx context.Context, req *pb.UpdateEquipmentRequest) (*pb.UpdateEquipmentResponse, error) {
	s.log.WithContext(ctx).Infof("Updating equipment: %d", req.Id)

	// Get existing equipment
	equipment, err := s.equipmentUc.GetEquipment(ctx, req.Id)
	if err != nil {
		return nil, err
	}

	// Update fields
	if req.Name != "" {
		equipment.Name = req.Name
	}
	if req.Location != "" {
		equipment.Location = req.Location
	}
	if req.Latitude != 0 {
		equipment.Latitude = &req.Latitude
	}
	if req.Longitude != 0 {
		equipment.Longitude = &req.Longitude
	}
	if req.Status != pb.EquipmentStatus_EQUIPMENT_STATUS_UNSPECIFIED {
		equipment.Status = convertProtoStatusToEntity(req.Status)
	}
	if req.Specifications != nil {
		equipment.Specifications = convertStructToJSON(req.Specifications)
	}
	if req.Metadata != nil {
		equipment.Metadata = convertStructToJSON(req.Metadata)
	}

	result, err := s.equipmentUc.UpdateEquipment(ctx, equipment)
	if err != nil {
		return nil, err
	}

	return &pb.UpdateEquipmentResponse{
		Equipment: convertEquipmentToProto(result),
	}, nil
}

// DeleteEquipment deletes equipment by ID
func (s *EquipmentService) DeleteEquipment(ctx context.Context, req *pb.DeleteEquipmentRequest) (*pb.DeleteEquipmentResponse, error) {
	s.log.WithContext(ctx).Infof("Deleting equipment: %d", req.Id)

	err := s.equipmentUc.DeleteEquipment(ctx, req.Id)
	if err != nil {
		return nil, err
	}

	return &pb.DeleteEquipmentResponse{
		Success: true,
	}, nil
}

// ============================================================================
// HEALTH MONITORING
// ============================================================================

// GetEquipmentHealth retrieves equipment health data
func (s *EquipmentService) GetEquipmentHealth(ctx context.Context, req *pb.GetEquipmentHealthRequest) (*pb.GetEquipmentHealthResponse, error) {
	s.log.WithContext(ctx).Infof("Getting equipment health: %d", req.Id)

	equipment, metrics, err := s.equipmentUc.GetEquipmentHealth(ctx, req.Id)
	if err != nil {
		return nil, err
	}

	// Convert health metrics to proto
	pbMetrics := make([]*pb.HealthMetric, len(metrics))
	for i, metric := range metrics {
		pbMetrics[i] = convertHealthMetricToProto(metric)
	}

	// Create health object
	health := &pb.EquipmentHealth{
		HealthScore:      equipment.HealthScore,
		HealthStatus:     equipment.HealthStatus,
		Metrics:          pbMetrics,
		LastAssessment:   timestamppb.New(equipment.UpdatedAt),
		AssessmentMethod: "manual", // Default
	}

	// Get trends for key metrics (simplified)
	trends := []*pb.HealthTrend{}
	if len(metrics) > 0 {
		// Create a sample trend for the first metric
		dataPoints := []*pb.HealthDataPoint{}
		for _, metric := range metrics[:min(10, len(metrics))] {
			dataPoints = append(dataPoints, &pb.HealthDataPoint{
				Timestamp: timestamppb.New(metric.RecordedAt),
				Value:     metric.Value,
			})
		}

		if len(dataPoints) > 0 {
			trends = append(trends, &pb.HealthTrend{
				MetricName: metrics[0].Name,
				DataPoints: dataPoints,
			})
		}
	}

	return &pb.GetEquipmentHealthResponse{
		Health: health,
		Trends: trends,
	}, nil
}

// UpdateEquipmentHealth updates equipment health metrics
func (s *EquipmentService) UpdateEquipmentHealth(ctx context.Context, req *pb.UpdateEquipmentHealthRequest) (*pb.UpdateEquipmentHealthResponse, error) {
	s.log.WithContext(ctx).Infof("Updating equipment health: %d", req.Id)

	// Convert proto metrics to entity metrics
	metrics := make([]*entity.HealthMetric, len(req.Metrics))
	for i, pbMetric := range req.Metrics {
		metrics[i] = convertProtoHealthMetricToEntity(pbMetric)
	}

	err := s.equipmentUc.UpdateEquipmentHealth(ctx, req.Id, metrics)
	if err != nil {
		return nil, err
	}

	// Get updated equipment to return health
	equipment, _, err := s.equipmentUc.GetEquipmentHealth(ctx, req.Id)
	if err != nil {
		return nil, err
	}

	health := &pb.EquipmentHealth{
		HealthScore:      equipment.HealthScore,
		HealthStatus:     equipment.HealthStatus,
		LastAssessment:   timestamppb.New(equipment.UpdatedAt),
		AssessmentMethod: req.AssessmentMethod,
	}

	return &pb.UpdateEquipmentHealthResponse{
		Health: health,
	}, nil
}

// ============================================================================
// MAINTENANCE MANAGEMENT
// ============================================================================

// ScheduleMaintenance schedules maintenance for equipment
func (s *EquipmentService) ScheduleMaintenance(ctx context.Context, req *pb.ScheduleMaintenanceRequest) (*pb.ScheduleMaintenanceResponse, error) {
	s.log.WithContext(ctx).Infof("Scheduling maintenance for equipment: %d", req.Id)

	schedule := &entity.MaintenanceSchedule{
		EquipmentID:              req.Id,
		MaintenanceType:          req.MaintenanceType,
		ScheduledDate:            req.ScheduledDate.AsTime(),
		Description:              req.Description,
		EstimatedDurationMinutes: req.EstimatedDurationMinutes,
		Status:                   "scheduled",
	}

	if req.TechnicianId > 0 {
		schedule.TechnicianID = &req.TechnicianId
	}

	// Convert required parts to JSON
	if len(req.RequiredParts) > 0 {
		partsJSON, _ := convertStringSliceToJSON(req.RequiredParts)
		schedule.RequiredParts = partsJSON
	}

	result, err := s.equipmentUc.ScheduleMaintenance(ctx, schedule)
	if err != nil {
		return nil, err
	}

	return &pb.ScheduleMaintenanceResponse{
		Schedule: convertMaintenanceScheduleToProto(result),
	}, nil
}

// GetMaintenanceHistory retrieves maintenance history for equipment
func (s *EquipmentService) GetMaintenanceHistory(ctx context.Context, req *pb.GetMaintenanceHistoryRequest) (*pb.GetMaintenanceHistoryResponse, error) {
	s.log.WithContext(ctx).Infof("Getting maintenance history for equipment: %d", req.Id)

	records, total, err := s.equipmentUc.GetMaintenanceHistory(ctx, req.Id, req.Page, req.PageSize, req.MaintenanceType)
	if err != nil {
		return nil, err
	}

	pbRecords := make([]*pb.MaintenanceRecord, len(records))
	for i, record := range records {
		pbRecords[i] = convertMaintenanceRecordToProto(record)
	}

	return &pb.GetMaintenanceHistoryResponse{
		Records:    pbRecords,
		TotalCount: total,
	}, nil
}

// ============================================================================
// PARTS MANAGEMENT
// ============================================================================

// GetEquipmentParts retrieves parts for equipment
func (s *EquipmentService) GetEquipmentParts(ctx context.Context, req *pb.GetEquipmentPartsRequest) (*pb.GetEquipmentPartsResponse, error) {
	s.log.WithContext(ctx).Infof("Getting equipment parts: %d", req.Id)

	parts, err := s.equipmentUc.GetEquipmentParts(ctx, req.Id)
	if err != nil {
		return nil, err
	}

	pbParts := make([]*pb.EquipmentPart, len(parts))
	for i, part := range parts {
		pbParts[i] = convertEquipmentPartToProto(part)
	}

	return &pb.GetEquipmentPartsResponse{
		Parts: pbParts,
	}, nil
}

// UpdatePartStatus updates the status of an equipment part
func (s *EquipmentService) UpdatePartStatus(ctx context.Context, req *pb.UpdatePartStatusRequest) (*pb.UpdatePartStatusResponse, error) {
	s.log.WithContext(ctx).Infof("Updating part status: equipment=%d, part=%d", req.Id, req.PartId)

	var replacementDate *time.Time
	if req.ReplacementDate != nil {
		date := req.ReplacementDate.AsTime()
		replacementDate = &date
	}

	part, err := s.equipmentUc.UpdatePartStatus(ctx, req.Id, req.PartId, req.Status, replacementDate, req.Notes)
	if err != nil {
		return nil, err
	}

	return &pb.UpdatePartStatusResponse{
		Part: convertEquipmentPartToProto(part),
	}, nil
}

// ============================================================================
// ANALYTICS AND REPORTING
// ============================================================================

// GetEquipmentAnalytics retrieves analytics data for equipment
func (s *EquipmentService) GetEquipmentAnalytics(ctx context.Context, req *pb.GetEquipmentAnalyticsRequest) (*pb.GetEquipmentAnalyticsResponse, error) {
	s.log.WithContext(ctx).Infof("Getting equipment analytics: %d", req.Id)

	startDate := time.Now().AddDate(0, -3, 0) // Default to 3 months
	endDate := time.Now()

	if req.StartDate != nil {
		startDate = req.StartDate.AsTime()
	}
	if req.EndDate != nil {
		endDate = req.EndDate.AsTime()
	}

	analytics, err := s.equipmentUc.GetEquipmentAnalytics(ctx, req.Id, startDate, endDate)
	if err != nil {
		return nil, err
	}

	return &pb.GetEquipmentAnalyticsResponse{
		Analytics: convertEquipmentAnalyticsToProto(analytics),
	}, nil
}

// GetFleetOverview retrieves fleet overview data
func (s *EquipmentService) GetFleetOverview(ctx context.Context, req *pb.GetFleetOverviewRequest) (*pb.GetFleetOverviewResponse, error) {
	s.log.WithContext(ctx).Info("Getting fleet overview")

	overview, err := s.equipmentUc.GetFleetOverview(ctx, req.CustomerId, req.EquipmentType)
	if err != nil {
		return nil, err
	}

	return &pb.GetFleetOverviewResponse{
		Overview: convertFleetOverviewToProto(overview),
	}, nil
}

// GenerateQRCode generates QR code for equipment
func (s *EquipmentService) GenerateQRCode(ctx context.Context, req *pb.GenerateQRCodeRequest) (*pb.GenerateQRCodeResponse, error) {
	s.log.WithContext(ctx).Infof("Generating QR code for equipment: %d", req.Id)

	// For now, return a placeholder URL
	// In a real implementation, this would generate an actual QR code
	qrCodeURL := "https://api.qrserver.com/v1/create-qr-code/?size=200x200&data=equipment:" + string(rune(req.Id))

	return &pb.GenerateQRCodeResponse{
		QrCodeUrl:  qrCodeURL,
		QrCodeData: []byte("placeholder-qr-data"),
		Format:     req.Format,
	}, nil
}

// ============================================================================
// HELPER FUNCTIONS
// ============================================================================

// min returns the minimum of two integers
func min(a, b int) int {
	if a < b {
		return a
	}
	return b
}

// convertEquipmentToProto converts entity equipment to protobuf
func convertEquipmentToProto(equipment *entity.Equipment) *pb.Equipment {
	pbEquipment := &pb.Equipment{
		Id:           equipment.ID,
		CustomerId:   equipment.CustomerID,
		Name:         equipment.Name,
		Type:         equipment.Type,
		Brand:        equipment.Brand,
		Model:        equipment.Model,
		SerialNumber: equipment.SerialNumber,
		Location:     equipment.Location,
		Status:       convertEntityStatusToProto(equipment.Status),
		CreatedAt:    timestamppb.New(equipment.CreatedAt),
		UpdatedAt:    timestamppb.New(equipment.UpdatedAt),
	}

	// Handle optional fields
	if equipment.InstallationDate != nil {
		pbEquipment.InstallationDate = timestamppb.New(*equipment.InstallationDate)
	}
	if equipment.WarrantyExpiry != nil {
		pbEquipment.WarrantyExpiry = timestamppb.New(*equipment.WarrantyExpiry)
	}
	if equipment.Latitude != nil {
		pbEquipment.Latitude = *equipment.Latitude
	}
	if equipment.Longitude != nil {
		pbEquipment.Longitude = *equipment.Longitude
	}

	// Convert health data
	pbEquipment.Health = &pb.EquipmentHealth{
		HealthScore:    equipment.HealthScore,
		HealthStatus:   equipment.HealthStatus,
		LastAssessment: timestamppb.New(equipment.UpdatedAt),
	}

	// Convert JSON fields to protobuf Struct
	if equipment.Specifications != nil {
		if specs, err := convertJSONToStruct(equipment.Specifications); err == nil {
			pbEquipment.Specifications = specs
		}
	}
	if equipment.Metadata != nil {
		if metadata, err := convertJSONToStruct(equipment.Metadata); err == nil {
			pbEquipment.Metadata = metadata
		}
	}

	return pbEquipment
}
