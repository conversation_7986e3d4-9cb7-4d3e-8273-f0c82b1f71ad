package service

import (
	"encoding/json"

	"google.golang.org/protobuf/types/known/structpb"
	"google.golang.org/protobuf/types/known/timestamppb"

	pb "gobackend-hvac-kratos/api/equipment/v1"
	"gobackend-hvac-kratos/internal/entity"
)

// 🔧 Equipment Service Helper Functions
// GoBackend-Kratos HVAC CRM System

// ============================================================================
// CONVERSION HELPERS
// ============================================================================

// convertStructToJSON converts protobuf Struct to JSON bytes
func convertStructToJSON(s *structpb.Struct) json.RawMessage {
	if s == nil {
		return nil
	}
	
	data, err := s.MarshalJSON()
	if err != nil {
		return nil
	}
	
	return json.RawMessage(data)
}

// convertJSONToStruct converts JSON bytes to protobuf Struct
func convertJSONToStruct(data json.RawMessage) (*structpb.Struct, error) {
	if data == nil {
		return nil, nil
	}
	
	var m map[string]interface{}
	if err := json.Unmarshal(data, &m); err != nil {
		return nil, err
	}
	
	return structpb.NewStruct(m)
}

// convertStringSliceToJSON converts string slice to JSON bytes
func convertStringSliceToJSON(slice []string) (json.RawMessage, error) {
	if slice == nil {
		return nil, nil
	}
	
	data, err := json.Marshal(slice)
	if err != nil {
		return nil, err
	}
	
	return json.RawMessage(data), nil
}

// convertEntityStatusToProto converts entity status to protobuf status
func convertEntityStatusToProto(status entity.EquipmentStatus) pb.EquipmentStatus {
	switch status {
	case entity.EquipmentStatusActive:
		return pb.EquipmentStatus_EQUIPMENT_STATUS_ACTIVE
	case entity.EquipmentStatusInactive:
		return pb.EquipmentStatus_EQUIPMENT_STATUS_INACTIVE
	case entity.EquipmentStatusMaintenance:
		return pb.EquipmentStatus_EQUIPMENT_STATUS_MAINTENANCE
	case entity.EquipmentStatusRetired:
		return pb.EquipmentStatus_EQUIPMENT_STATUS_RETIRED
	case entity.EquipmentStatusFaulty:
		return pb.EquipmentStatus_EQUIPMENT_STATUS_FAULTY
	default:
		return pb.EquipmentStatus_EQUIPMENT_STATUS_UNSPECIFIED
	}
}

// convertProtoStatusToEntity converts protobuf status to entity status
func convertProtoStatusToEntity(status pb.EquipmentStatus) entity.EquipmentStatus {
	switch status {
	case pb.EquipmentStatus_EQUIPMENT_STATUS_ACTIVE:
		return entity.EquipmentStatusActive
	case pb.EquipmentStatus_EQUIPMENT_STATUS_INACTIVE:
		return entity.EquipmentStatusInactive
	case pb.EquipmentStatus_EQUIPMENT_STATUS_MAINTENANCE:
		return entity.EquipmentStatusMaintenance
	case pb.EquipmentStatus_EQUIPMENT_STATUS_RETIRED:
		return entity.EquipmentStatusRetired
	case pb.EquipmentStatus_EQUIPMENT_STATUS_FAULTY:
		return entity.EquipmentStatusFaulty
	default:
		return entity.EquipmentStatusActive
	}
}

// convertHealthMetricToProto converts entity health metric to protobuf
func convertHealthMetricToProto(metric *entity.HealthMetric) *pb.HealthMetric {
	pbMetric := &pb.HealthMetric{
		Name:   metric.Name,
		Value:  metric.Value,
		Unit:   metric.Unit,
		Status: metric.Status,
	}
	
	if metric.ThresholdMin != nil {
		pbMetric.ThresholdMin = *metric.ThresholdMin
	}
	if metric.ThresholdMax != nil {
		pbMetric.ThresholdMax = *metric.ThresholdMax
	}
	
	return pbMetric
}

// convertProtoHealthMetricToEntity converts protobuf health metric to entity
func convertProtoHealthMetricToEntity(pbMetric *pb.HealthMetric) *entity.HealthMetric {
	metric := &entity.HealthMetric{
		Name:   pbMetric.Name,
		Value:  pbMetric.Value,
		Unit:   pbMetric.Unit,
		Status: pbMetric.Status,
	}
	
	if pbMetric.ThresholdMin != 0 {
		metric.ThresholdMin = &pbMetric.ThresholdMin
	}
	if pbMetric.ThresholdMax != 0 {
		metric.ThresholdMax = &pbMetric.ThresholdMax
	}
	
	return metric
}

// convertMaintenanceScheduleToProto converts entity maintenance schedule to protobuf
func convertMaintenanceScheduleToProto(schedule *entity.MaintenanceSchedule) *pb.MaintenanceSchedule {
	pbSchedule := &pb.MaintenanceSchedule{
		Id:                       schedule.ID,
		EquipmentId:              schedule.EquipmentID,
		MaintenanceType:          schedule.MaintenanceType,
		ScheduledDate:            timestamppb.New(schedule.ScheduledDate),
		Description:              schedule.Description,
		EstimatedDurationMinutes: schedule.EstimatedDurationMinutes,
		Status:                   schedule.Status,
		CreatedAt:                timestamppb.New(schedule.CreatedAt),
	}
	
	if schedule.TechnicianID != nil {
		pbSchedule.TechnicianId = *schedule.TechnicianID
	}
	
	// Convert required parts from JSON
	if schedule.RequiredParts != nil {
		var parts []string
		if err := json.Unmarshal(schedule.RequiredParts, &parts); err == nil {
			pbSchedule.RequiredParts = parts
		}
	}
	
	return pbSchedule
}

// convertMaintenanceRecordToProto converts entity maintenance record to protobuf
func convertMaintenanceRecordToProto(record *entity.MaintenanceRecord) *pb.MaintenanceRecord {
	pbRecord := &pb.MaintenanceRecord{
		Id:                    record.ID,
		EquipmentId:           record.EquipmentID,
		MaintenanceType:       record.MaintenanceType,
		PerformedDate:         timestamppb.New(record.PerformedDate),
		Description:           record.Description,
		ActualDurationMinutes: record.ActualDurationMinutes,
		Status:                record.Status,
		Notes:                 record.Notes,
		Cost:                  record.Cost,
		CreatedAt:             timestamppb.New(record.CreatedAt),
	}
	
	if record.TechnicianID != nil {
		pbRecord.TechnicianId = *record.TechnicianID
	}
	
	// Convert parts used from JSON
	if record.PartsUsed != nil {
		var parts []string
		if err := json.Unmarshal(record.PartsUsed, &parts); err == nil {
			pbRecord.PartsUsed = parts
		}
	}
	
	return pbRecord
}

// convertEquipmentPartToProto converts entity equipment part to protobuf
func convertEquipmentPartToProto(part *entity.EquipmentPart) *pb.EquipmentPart {
	pbPart := &pb.EquipmentPart{
		Id:           part.ID,
		EquipmentId:  part.EquipmentID,
		Name:         part.Name,
		PartNumber:   part.PartNumber,
		Manufacturer: part.Manufacturer,
		Status:       part.Status,
		Cost:         part.Cost,
		Supplier:     part.Supplier,
	}
	
	if part.InstallationDate != nil {
		pbPart.InstallationDate = timestamppb.New(*part.InstallationDate)
	}
	if part.WarrantyExpiry != nil {
		pbPart.WarrantyExpiry = timestamppb.New(*part.WarrantyExpiry)
	}
	
	// Convert specifications from JSON
	if part.Specifications != nil {
		if specs, err := convertJSONToStruct(part.Specifications); err == nil {
			pbPart.Specifications = specs
		}
	}
	
	return pbPart
}

// convertEquipmentAnalyticsToProto converts entity equipment analytics to protobuf
func convertEquipmentAnalyticsToProto(analytics *entity.EquipmentAnalytics) *pb.EquipmentAnalytics {
	pbMetrics := make([]*pb.PerformanceMetric, len(analytics.PerformanceMetrics))
	for i, metric := range analytics.PerformanceMetrics {
		pbMetrics[i] = &pb.PerformanceMetric{
			Name:      metric.Name,
			Value:     metric.Value,
			Unit:      metric.Unit,
			Benchmark: metric.Benchmark,
			Trend:     metric.Trend,
		}
	}
	
	return &pb.EquipmentAnalytics{
		EquipmentId:           analytics.EquipmentID,
		UptimePercentage:      analytics.UptimePercentage,
		MaintenanceCount:      analytics.MaintenanceCount,
		TotalMaintenanceCost:  analytics.TotalMaintenanceCost,
		EnergyEfficiencyScore: analytics.EnergyEfficiencyScore,
		PerformanceMetrics:    pbMetrics,
		AnalysisPeriodStart:   timestamppb.New(analytics.AnalysisPeriodStart),
		AnalysisPeriodEnd:     timestamppb.New(analytics.AnalysisPeriodEnd),
	}
}

// convertFleetOverviewToProto converts entity fleet overview to protobuf
func convertFleetOverviewToProto(overview *entity.FleetOverview) *pb.FleetOverview {
	pbSummaries := make([]*pb.EquipmentTypeSummary, len(overview.TypeSummaries))
	for i, summary := range overview.TypeSummaries {
		pbSummaries[i] = &pb.EquipmentTypeSummary{
			EquipmentType:      summary.EquipmentType,
			Count:              summary.Count,
			AverageHealthScore: summary.AverageHealthScore,
			MaintenanceDue:     summary.MaintenanceDue,
		}
	}
	
	return &pb.FleetOverview{
		TotalEquipment:     overview.TotalEquipment,
		ActiveEquipment:    overview.ActiveEquipment,
		MaintenanceDue:     overview.MaintenanceDue,
		CriticalHealth:     overview.CriticalHealth,
		AverageHealthScore: overview.AverageHealthScore,
		TotalFleetValue:    overview.TotalFleetValue,
		TypeSummaries:      pbSummaries,
	}
}
