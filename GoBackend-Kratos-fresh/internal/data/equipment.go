package data

import (
	"context"
	"time"

	"gobackend-hvac-kratos/internal/biz"
	"gobackend-hvac-kratos/internal/entity"

	"github.com/go-kratos/kratos/v2/log"
	"gorm.io/gorm"
)

// 🏭 Equipment Repository - Data Layer for Equipment Management
// GoBackend-Kratos HVAC CRM System

// equipmentRepo implements the equipment repository
type equipmentRepo struct {
	data *Data
	log  *log.Helper
}

// NewEquipmentRepo creates a new equipment repository
func NewEquipmentRepo(data *Data, logger log.Logger) biz.EquipmentRepo {
	return &equipmentRepo{
		data: data,
		log:  log.NewHelper(logger),
	}
}

// ============================================================================
// EQUIPMENT CRUD OPERATIONS
// ============================================================================

// CreateEquipment creates a new equipment record
func (r *equipmentRepo) CreateEquipment(ctx context.Context, equipment *entity.Equipment) (*entity.Equipment, error) {
	r.log.WithContext(ctx).Infof("Creating equipment in database: %s", equipment.Name)

	if err := r.data.db.WithContext(ctx).Create(equipment).Error; err != nil {
		r.log.WithContext(ctx).Errorf("Failed to create equipment: %v", err)
		return nil, err
	}

	return equipment, nil
}

// GetEquipment retrieves equipment by ID
func (r *equipmentRepo) GetEquipment(ctx context.Context, id int64) (*entity.Equipment, error) {
	r.log.WithContext(ctx).Infof("Getting equipment from database: %d", id)

	var equipment entity.Equipment
	err := r.data.db.WithContext(ctx).
		Preload("Customer").
		Preload("HealthMetrics").
		Preload("MaintenanceRecords").
		Preload("Parts").
		First(&equipment, id).Error

	if err != nil {
		if err == gorm.ErrRecordNotFound {
			return nil, biz.ErrEquipmentNotFound
		}
		r.log.WithContext(ctx).Errorf("Failed to get equipment: %v", err)
		return nil, err
	}

	return &equipment, nil
}

// ListEquipment retrieves equipment with filtering and pagination
func (r *equipmentRepo) ListEquipment(ctx context.Context, page, pageSize int32, customerID int64, equipmentType, status, healthStatus string) ([]*entity.Equipment, int32, error) {
	r.log.WithContext(ctx).Infof("Listing equipment from database: page=%d, size=%d", page, pageSize)

	var equipment []*entity.Equipment
	var total int64

	query := r.data.db.WithContext(ctx).Model(&entity.Equipment{})

	// Apply filters
	if customerID > 0 {
		query = query.Where("customer_id = ?", customerID)
	}
	if equipmentType != "" {
		query = query.Where("type = ?", equipmentType)
	}
	if status != "" {
		query = query.Where("status = ?", status)
	}
	if healthStatus != "" {
		query = query.Where("health_status = ?", healthStatus)
	}

	// Get total count
	if err := query.Count(&total).Error; err != nil {
		r.log.WithContext(ctx).Errorf("Failed to count equipment: %v", err)
		return nil, 0, err
	}

	// Apply pagination and get results
	offset := (page - 1) * pageSize
	err := query.
		Preload("Customer").
		Offset(int(offset)).
		Limit(int(pageSize)).
		Order("created_at DESC").
		Find(&equipment).Error

	if err != nil {
		r.log.WithContext(ctx).Errorf("Failed to list equipment: %v", err)
		return nil, 0, err
	}

	return equipment, int32(total), nil
}

// UpdateEquipment updates existing equipment
func (r *equipmentRepo) UpdateEquipment(ctx context.Context, equipment *entity.Equipment) (*entity.Equipment, error) {
	r.log.WithContext(ctx).Infof("Updating equipment in database: %d", equipment.ID)

	if err := r.data.db.WithContext(ctx).Save(equipment).Error; err != nil {
		r.log.WithContext(ctx).Errorf("Failed to update equipment: %v", err)
		return nil, err
	}

	return equipment, nil
}

// DeleteEquipment deletes equipment by ID
func (r *equipmentRepo) DeleteEquipment(ctx context.Context, id int64) error {
	r.log.WithContext(ctx).Infof("Deleting equipment from database: %d", id)

	result := r.data.db.WithContext(ctx).Delete(&entity.Equipment{}, id)
	if result.Error != nil {
		r.log.WithContext(ctx).Errorf("Failed to delete equipment: %v", result.Error)
		return result.Error
	}

	if result.RowsAffected == 0 {
		return biz.ErrEquipmentNotFound
	}

	return nil
}

// ============================================================================
// HEALTH MANAGEMENT
// ============================================================================

// GetEquipmentHealth retrieves equipment health data and metrics
func (r *equipmentRepo) GetEquipmentHealth(ctx context.Context, equipmentID int64) (*entity.Equipment, []*entity.HealthMetric, error) {
	r.log.WithContext(ctx).Infof("Getting equipment health from database: %d", equipmentID)

	var equipment entity.Equipment
	err := r.data.db.WithContext(ctx).First(&equipment, equipmentID).Error
	if err != nil {
		if err == gorm.ErrRecordNotFound {
			return nil, nil, biz.ErrEquipmentNotFound
		}
		return nil, nil, err
	}

	var metrics []*entity.HealthMetric
	err = r.data.db.WithContext(ctx).
		Where("equipment_id = ?", equipmentID).
		Order("recorded_at DESC").
		Limit(100). // Get latest 100 metrics
		Find(&metrics).Error

	if err != nil {
		r.log.WithContext(ctx).Errorf("Failed to get health metrics: %v", err)
		return nil, nil, err
	}

	return &equipment, metrics, nil
}

// UpdateEquipmentHealth updates equipment health metrics
func (r *equipmentRepo) UpdateEquipmentHealth(ctx context.Context, equipmentID int64, metrics []*entity.HealthMetric) error {
	r.log.WithContext(ctx).Infof("Updating equipment health in database: %d", equipmentID)

	// Set equipment ID for all metrics
	for _, metric := range metrics {
		metric.EquipmentID = equipmentID
		metric.RecordedAt = time.Now()
		metric.CreatedAt = time.Now()
	}

	// Create metrics in batch
	if err := r.data.db.WithContext(ctx).Create(&metrics).Error; err != nil {
		r.log.WithContext(ctx).Errorf("Failed to create health metrics: %v", err)
		return err
	}

	return nil
}

// GetHealthTrends retrieves health trends for specific metrics
func (r *equipmentRepo) GetHealthTrends(ctx context.Context, equipmentID int64, metricName string, days int) ([]*entity.HealthMetric, error) {
	r.log.WithContext(ctx).Infof("Getting health trends from database: equipment=%d, metric=%s, days=%d", equipmentID, metricName, days)

	var metrics []*entity.HealthMetric
	startDate := time.Now().AddDate(0, 0, -days)

	query := r.data.db.WithContext(ctx).
		Where("equipment_id = ? AND recorded_at >= ?", equipmentID, startDate).
		Order("recorded_at ASC")

	if metricName != "" {
		query = query.Where("name = ?", metricName)
	}

	err := query.Find(&metrics).Error
	if err != nil {
		r.log.WithContext(ctx).Errorf("Failed to get health trends: %v", err)
		return nil, err
	}

	return metrics, nil
}

// ============================================================================
// MAINTENANCE MANAGEMENT
// ============================================================================

// ScheduleMaintenance creates a maintenance schedule
func (r *equipmentRepo) ScheduleMaintenance(ctx context.Context, schedule *entity.MaintenanceSchedule) (*entity.MaintenanceSchedule, error) {
	r.log.WithContext(ctx).Infof("Scheduling maintenance in database: equipment=%d", schedule.EquipmentID)

	if err := r.data.db.WithContext(ctx).Create(schedule).Error; err != nil {
		r.log.WithContext(ctx).Errorf("Failed to schedule maintenance: %v", err)
		return nil, err
	}

	return schedule, nil
}

// GetMaintenanceHistory retrieves maintenance history for equipment
func (r *equipmentRepo) GetMaintenanceHistory(ctx context.Context, equipmentID int64, page, pageSize int32, maintenanceType string) ([]*entity.MaintenanceRecord, int32, error) {
	r.log.WithContext(ctx).Infof("Getting maintenance history from database: equipment=%d", equipmentID)

	var records []*entity.MaintenanceRecord
	var total int64

	query := r.data.db.WithContext(ctx).
		Model(&entity.MaintenanceRecord{}).
		Where("equipment_id = ?", equipmentID)

	if maintenanceType != "" {
		query = query.Where("maintenance_type = ?", maintenanceType)
	}

	// Get total count
	if err := query.Count(&total).Error; err != nil {
		r.log.WithContext(ctx).Errorf("Failed to count maintenance records: %v", err)
		return nil, 0, err
	}

	// Apply pagination and get results
	offset := (page - 1) * pageSize
	err := query.
		Offset(int(offset)).
		Limit(int(pageSize)).
		Order("performed_date DESC").
		Find(&records).Error

	if err != nil {
		r.log.WithContext(ctx).Errorf("Failed to get maintenance history: %v", err)
		return nil, 0, err
	}

	return records, int32(total), nil
}

// CreateMaintenanceRecord creates a maintenance record
func (r *equipmentRepo) CreateMaintenanceRecord(ctx context.Context, record *entity.MaintenanceRecord) (*entity.MaintenanceRecord, error) {
	r.log.WithContext(ctx).Infof("Creating maintenance record in database: equipment=%d", record.EquipmentID)

	if err := r.data.db.WithContext(ctx).Create(record).Error; err != nil {
		r.log.WithContext(ctx).Errorf("Failed to create maintenance record: %v", err)
		return nil, err
	}

	return record, nil
}

// ============================================================================
// PARTS MANAGEMENT
// ============================================================================

// GetEquipmentParts retrieves parts for equipment
func (r *equipmentRepo) GetEquipmentParts(ctx context.Context, equipmentID int64) ([]*entity.EquipmentPart, error) {
	r.log.WithContext(ctx).Infof("Getting equipment parts from database: %d", equipmentID)

	var parts []*entity.EquipmentPart
	err := r.data.db.WithContext(ctx).
		Where("equipment_id = ?", equipmentID).
		Order("created_at DESC").
		Find(&parts).Error

	if err != nil {
		r.log.WithContext(ctx).Errorf("Failed to get equipment parts: %v", err)
		return nil, err
	}

	return parts, nil
}

// UpdatePartStatus updates the status of an equipment part
func (r *equipmentRepo) UpdatePartStatus(ctx context.Context, equipmentID, partID int64, status string, replacementDate *time.Time, notes string) (*entity.EquipmentPart, error) {
	r.log.WithContext(ctx).Infof("Updating part status in database: equipment=%d, part=%d", equipmentID, partID)

	var part entity.EquipmentPart
	err := r.data.db.WithContext(ctx).
		Where("id = ? AND equipment_id = ?", partID, equipmentID).
		First(&part).Error

	if err != nil {
		if err == gorm.ErrRecordNotFound {
			return nil, biz.ErrEquipmentNotFound
		}
		return nil, err
	}

	// Update part status
	part.Status = status
	if replacementDate != nil {
		part.InstallationDate = replacementDate
	}
	part.UpdatedAt = time.Now()

	if err := r.data.db.WithContext(ctx).Save(&part).Error; err != nil {
		r.log.WithContext(ctx).Errorf("Failed to update part status: %v", err)
		return nil, err
	}

	return &part, nil
}

// ============================================================================
// ANALYTICS
// ============================================================================

// GetEquipmentAnalytics retrieves analytics data for equipment
func (r *equipmentRepo) GetEquipmentAnalytics(ctx context.Context, equipmentID int64, startDate, endDate time.Time) (*entity.EquipmentAnalytics, error) {
	r.log.WithContext(ctx).Infof("Getting equipment analytics from database: %d", equipmentID)

	// Get basic equipment info
	var equipment entity.Equipment
	err := r.data.db.WithContext(ctx).First(&equipment, equipmentID).Error
	if err != nil {
		if err == gorm.ErrRecordNotFound {
			return nil, biz.ErrEquipmentNotFound
		}
		return nil, err
	}

	// Calculate maintenance count and cost
	var maintenanceCount int64
	var totalCost float64

	r.data.db.WithContext(ctx).
		Model(&entity.MaintenanceRecord{}).
		Where("equipment_id = ? AND performed_date BETWEEN ? AND ?", equipmentID, startDate, endDate).
		Count(&maintenanceCount)

	r.data.db.WithContext(ctx).
		Model(&entity.MaintenanceRecord{}).
		Where("equipment_id = ? AND performed_date BETWEEN ? AND ?", equipmentID, startDate, endDate).
		Select("COALESCE(SUM(cost), 0)").
		Scan(&totalCost)

	// Calculate uptime percentage (simplified calculation)
	uptimePercentage := 100.0
	if equipment.Status == entity.EquipmentStatusFaulty {
		uptimePercentage = 85.0
	} else if equipment.Status == entity.EquipmentStatusMaintenance {
		uptimePercentage = 95.0
	}

	// Create performance metrics
	performanceMetrics := []entity.PerformanceMetric{
		{
			Name:      "Health Score",
			Value:     equipment.HealthScore * 100,
			Unit:      "%",
			Benchmark: 90.0,
			Trend:     "stable",
		},
		{
			Name:      "Uptime",
			Value:     uptimePercentage,
			Unit:      "%",
			Benchmark: 98.0,
			Trend:     "stable",
		},
	}

	analytics := &entity.EquipmentAnalytics{
		EquipmentID:           equipmentID,
		UptimePercentage:      uptimePercentage,
		MaintenanceCount:      int32(maintenanceCount),
		TotalMaintenanceCost:  totalCost,
		EnergyEfficiencyScore: equipment.HealthScore * 100, // Simplified
		PerformanceMetrics:    performanceMetrics,
		AnalysisPeriodStart:   startDate,
		AnalysisPeriodEnd:     endDate,
	}

	return analytics, nil
}

// GetFleetOverview retrieves fleet overview data
func (r *equipmentRepo) GetFleetOverview(ctx context.Context, customerID int64, equipmentType string) (*entity.FleetOverview, error) {
	r.log.WithContext(ctx).Infof("Getting fleet overview from database")

	query := r.data.db.WithContext(ctx).Model(&entity.Equipment{})

	// Apply filters
	if customerID > 0 {
		query = query.Where("customer_id = ?", customerID)
	}
	if equipmentType != "" {
		query = query.Where("type = ?", equipmentType)
	}

	// Get total equipment count
	var totalEquipment int64
	query.Count(&totalEquipment)

	// Get active equipment count
	var activeEquipment int64
	query.Where("status = ?", entity.EquipmentStatusActive).Count(&activeEquipment)

	// Get equipment needing maintenance (health score < 0.5)
	var maintenanceDue int64
	query.Where("health_score < ?", 0.5).Count(&maintenanceDue)

	// Get critical health equipment (health score < 0.3)
	var criticalHealth int64
	query.Where("health_score < ?", 0.3).Count(&criticalHealth)

	// Calculate average health score
	var avgHealthScore float64
	query.Select("COALESCE(AVG(health_score), 1.0)").Scan(&avgHealthScore)

	// Get equipment type summaries
	var typeSummaries []entity.EquipmentTypeSummary
	rows, err := r.data.db.WithContext(ctx).
		Model(&entity.Equipment{}).
		Select("type as equipment_type, COUNT(*) as count, AVG(health_score) as average_health_score, SUM(CASE WHEN health_score < 0.5 THEN 1 ELSE 0 END) as maintenance_due").
		Group("type").
		Rows()

	if err != nil {
		r.log.WithContext(ctx).Errorf("Failed to get type summaries: %v", err)
		return nil, err
	}
	defer rows.Close()

	for rows.Next() {
		var summary entity.EquipmentTypeSummary
		err := rows.Scan(&summary.EquipmentType, &summary.Count, &summary.AverageHealthScore, &summary.MaintenanceDue)
		if err != nil {
			continue
		}
		typeSummaries = append(typeSummaries, summary)
	}

	overview := &entity.FleetOverview{
		TotalEquipment:     int32(totalEquipment),
		ActiveEquipment:    int32(activeEquipment),
		MaintenanceDue:     int32(maintenanceDue),
		CriticalHealth:     int32(criticalHealth),
		AverageHealthScore: avgHealthScore,
		TotalFleetValue:    0, // Would need equipment value data
		TypeSummaries:      typeSummaries,
	}

	return overview, nil
}
