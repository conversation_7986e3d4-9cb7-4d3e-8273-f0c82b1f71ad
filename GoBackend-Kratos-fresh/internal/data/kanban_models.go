package data

import (
	"database/sql/driver"
	"encoding/json"
	"fmt"
	"time"
)

// 🎯 AMAZING COMPLEX KANBAN SYSTEM FOR HVAC CRM
// Comprehensive workflow management with AI-powered insights

// KanbanBoardType represents different types of Kanban boards
type KanbanBoardType string

const (
	BoardTypeSalesPipeline     KanbanBoardType = "sales_pipeline"
	BoardTypeServiceOrders     KanbanBoardType = "service_orders"
	BoardTypeCustomerLifecycle KanbanBoardType = "customer_lifecycle"
	BoardTypeEquipmentMaint    KanbanBoardType = "equipment_maintenance"
	BoardTypeProjectManagement KanbanBoardType = "project_management"
)

// KanbanCardType represents different types of cards
type KanbanCardType string

const (
	CardTypeLead         KanbanCardType = "lead"
	CardTypeCustomer     KanbanCardType = "customer"
	CardTypeServiceOrder KanbanCardType = "service_order"
	CardTypeProject      KanbanCardType = "project"
	CardTypeEquipment    KanbanCardType = "equipment"
	CardTypeOpportunity  KanbanCardType = "opportunity"
)

// Priority levels for Kanban cards
type KanbanPriority string

const (
	KanbanPriorityLow      KanbanPriority = "low"
	KanbanPriorityNormal   KanbanPriority = "normal"
	KanbanPriorityHigh     KanbanPriority = "high"
	KanbanPriorityUrgent   KanbanPriority = "urgent"
	KanbanPriorityCritical KanbanPriority = "critical"
)

// KanbanMetadata represents flexible metadata for cards
type KanbanMetadata map[string]interface{}

// Scan implements the sql.Scanner interface
func (km *KanbanMetadata) Scan(value interface{}) error {
	if value == nil {
		*km = nil
		return nil
	}

	switch v := value.(type) {
	case []byte:
		return json.Unmarshal(v, km)
	case string:
		return json.Unmarshal([]byte(v), km)
	default:
		return fmt.Errorf("cannot scan %T into KanbanMetadata", value)
	}
}

// Value implements the driver.Valuer interface
func (km KanbanMetadata) Value() (driver.Value, error) {
	if km == nil {
		return nil, nil
	}
	return json.Marshal(km)
}

// 📋 KanbanBoard represents a workflow board
type KanbanBoard struct {
	ID          int64           `gorm:"primaryKey;autoIncrement" json:"id"`
	Name        string          `gorm:"not null" json:"name"`
	Description string          `gorm:"type:text" json:"description"`
	BoardType   KanbanBoardType `gorm:"not null" json:"board_type"`
	IsActive    bool            `gorm:"default:true" json:"is_active"`
	Position    int             `gorm:"default:0" json:"position"`
	Color       string          `gorm:"default:#3498db" json:"color"`
	Icon        string          `gorm:"default:📋" json:"icon"`
	CreatedBy   int64           `json:"created_by"`
	CreatedAt   time.Time       `gorm:"autoCreateTime" json:"created_at"`
	UpdatedAt   time.Time       `gorm:"autoUpdateTime" json:"updated_at"`

	// Relationships
	Columns []*KanbanColumn `gorm:"foreignKey:BoardID;constraint:OnDelete:CASCADE" json:"columns,omitempty"`
	Cards   []*KanbanCard   `gorm:"foreignKey:BoardID;constraint:OnDelete:CASCADE" json:"cards,omitempty"`
}

// 📊 KanbanColumn represents a stage in the workflow
type KanbanColumn struct {
	ID          int64     `gorm:"primaryKey;autoIncrement" json:"id"`
	BoardID     int64     `gorm:"not null;index" json:"board_id"`
	Name        string    `gorm:"not null" json:"name"`
	Description string    `gorm:"type:text" json:"description"`
	Position    int       `gorm:"not null" json:"position"`
	Color       string    `gorm:"default:#ecf0f1" json:"color"`
	Icon        string    `gorm:"default:📝" json:"icon"`
	WIPLimit    int       `gorm:"default:0" json:"wip_limit"` // Work In Progress limit
	IsCompleted bool      `gorm:"default:false" json:"is_completed"`
	CreatedAt   time.Time `gorm:"autoCreateTime" json:"created_at"`
	UpdatedAt   time.Time `gorm:"autoUpdateTime" json:"updated_at"`

	// Relationships
	Board *KanbanBoard  `gorm:"foreignKey:BoardID;constraint:OnDelete:CASCADE" json:"board,omitempty"`
	Cards []*KanbanCard `gorm:"foreignKey:ColumnID;constraint:OnDelete:CASCADE" json:"cards,omitempty"`
}

// 🎴 KanbanCard represents an item in the workflow
type KanbanCard struct {
	ID          int64          `gorm:"primaryKey;autoIncrement" json:"id"`
	BoardID     int64          `gorm:"not null;index" json:"board_id"`
	ColumnID    int64          `gorm:"not null;index" json:"column_id"`
	Title       string         `gorm:"not null" json:"title"`
	Description string         `gorm:"type:text" json:"description"`
	CardType    KanbanCardType `gorm:"not null" json:"card_type"`
	Priority    KanbanPriority `gorm:"default:normal" json:"priority"`
	Position    int            `gorm:"not null" json:"position"`
	Color       string         `gorm:"default:#ffffff" json:"color"`
	Tags        StringArray    `gorm:"type:text[]" json:"tags"`

	// Reference to actual entities
	EntityID   *int64 `json:"entity_id"`   // ID of lead, customer, service order, etc.
	EntityType string `json:"entity_type"` // "lead", "customer", "service_order", etc.

	// Workflow tracking
	EstimatedHours *float64   `gorm:"type:decimal(5,2)" json:"estimated_hours"`
	ActualHours    *float64   `gorm:"type:decimal(5,2)" json:"actual_hours"`
	DueDate        *time.Time `json:"due_date"`
	StartedAt      *time.Time `json:"started_at"`
	CompletedAt    *time.Time `json:"completed_at"`

	// Assignment
	AssignedTo   *int64      `json:"assigned_to"` // User ID
	AssignedTeam StringArray `gorm:"type:text[]" json:"assigned_team"`

	// Business data
	EstimatedValue *float64 `gorm:"type:decimal(10,2)" json:"estimated_value"`
	ActualValue    *float64 `gorm:"type:decimal(10,2)" json:"actual_value"`

	// Metadata for flexibility
	Metadata KanbanMetadata `gorm:"type:jsonb" json:"metadata"`

	// Tracking
	CreatedBy int64     `json:"created_by"`
	CreatedAt time.Time `gorm:"autoCreateTime" json:"created_at"`
	UpdatedAt time.Time `gorm:"autoUpdateTime" json:"updated_at"`

	// Relationships
	Board       *KanbanBoard        `gorm:"foreignKey:BoardID;constraint:OnDelete:CASCADE" json:"board,omitempty"`
	Column      *KanbanColumn       `gorm:"foreignKey:ColumnID;constraint:OnDelete:CASCADE" json:"column,omitempty"`
	Activities  []*KanbanActivity   `gorm:"foreignKey:CardID;constraint:OnDelete:CASCADE" json:"activities,omitempty"`
	Comments    []*KanbanComment    `gorm:"foreignKey:CardID;constraint:OnDelete:CASCADE" json:"comments,omitempty"`
	Attachments []*KanbanAttachment `gorm:"foreignKey:CardID;constraint:OnDelete:CASCADE" json:"attachments,omitempty"`
}

// 📝 KanbanActivity tracks all card movements and changes
type KanbanActivity struct {
	ID           int64          `gorm:"primaryKey;autoIncrement" json:"id"`
	CardID       int64          `gorm:"not null;index" json:"card_id"`
	ActivityType string         `gorm:"not null" json:"activity_type"` // moved, created, updated, assigned, etc.
	Description  string         `gorm:"not null" json:"description"`
	OldValue     string         `json:"old_value"`
	NewValue     string         `json:"new_value"`
	Metadata     KanbanMetadata `gorm:"type:jsonb" json:"metadata"`
	CreatedBy    int64          `json:"created_by"`
	CreatedAt    time.Time      `gorm:"autoCreateTime" json:"created_at"`

	// Relationships
	Card *KanbanCard `gorm:"foreignKey:CardID;constraint:OnDelete:CASCADE" json:"card,omitempty"`
}

// 💬 KanbanComment represents comments on cards
type KanbanComment struct {
	ID        int64     `gorm:"primaryKey;autoIncrement" json:"id"`
	CardID    int64     `gorm:"not null;index" json:"card_id"`
	Content   string    `gorm:"type:text;not null" json:"content"`
	CreatedBy int64     `json:"created_by"`
	CreatedAt time.Time `gorm:"autoCreateTime" json:"created_at"`
	UpdatedAt time.Time `gorm:"autoUpdateTime" json:"updated_at"`

	// Relationships
	Card *KanbanCard `gorm:"foreignKey:CardID;constraint:OnDelete:CASCADE" json:"card,omitempty"`
}

// 📎 KanbanAttachment represents file attachments on cards
type KanbanAttachment struct {
	ID           int64     `gorm:"primaryKey;autoIncrement" json:"id"`
	CardID       int64     `gorm:"not null;index" json:"card_id"`
	Filename     string    `gorm:"not null" json:"filename"`
	OriginalName string    `gorm:"not null" json:"original_name"`
	ContentType  string    `json:"content_type"`
	Size         int64     `json:"size"`
	URL          string    `json:"url"`
	CreatedBy    int64     `json:"created_by"`
	CreatedAt    time.Time `gorm:"autoCreateTime" json:"created_at"`

	// Relationships
	Card *KanbanCard `gorm:"foreignKey:CardID;constraint:OnDelete:CASCADE" json:"card,omitempty"`
}

// Table names
func (KanbanBoard) TableName() string      { return "kanban_boards" }
func (KanbanColumn) TableName() string     { return "kanban_columns" }
func (KanbanCard) TableName() string       { return "kanban_cards" }
func (KanbanActivity) TableName() string   { return "kanban_activities" }
func (KanbanComment) TableName() string    { return "kanban_comments" }
func (KanbanAttachment) TableName() string { return "kanban_attachments" }

// Helper methods for KanbanCard
func (kc *KanbanCard) IsOverdue() bool {
	if kc.DueDate == nil || kc.CompletedAt != nil {
		return false
	}
	return time.Now().After(*kc.DueDate)
}

func (kc *KanbanCard) GetDaysInColumn() int {
	if kc.StartedAt == nil {
		return int(time.Since(kc.CreatedAt).Hours() / 24)
	}
	return int(time.Since(*kc.StartedAt).Hours() / 24)
}

func (kc *KanbanCard) GetProgressPercentage() float64 {
	if kc.EstimatedHours == nil || *kc.EstimatedHours == 0 {
		return 0
	}
	if kc.ActualHours == nil {
		return 0
	}
	return (*kc.ActualHours / *kc.EstimatedHours) * 100
}

func (kc *KanbanCard) GetPriorityColor() string {
	switch kc.Priority {
	case KanbanPriorityCritical:
		return "#e74c3c"
	case KanbanPriorityUrgent:
		return "#f39c12"
	case KanbanPriorityHigh:
		return "#fd7e14"
	case KanbanPriorityNormal:
		return "#3498db"
	case KanbanPriorityLow:
		return "#95a5a6"
	default:
		return "#3498db"
	}
}

func (kc *KanbanCard) GetPriorityIcon() string {
	switch kc.Priority {
	case KanbanPriorityCritical:
		return "🔥"
	case KanbanPriorityUrgent:
		return "⚡"
	case KanbanPriorityHigh:
		return "🔴"
	case KanbanPriorityNormal:
		return "🔵"
	case KanbanPriorityLow:
		return "⚪"
	default:
		return "🔵"
	}
}

// Helper methods for KanbanColumn
func (kc *KanbanColumn) IsWIPLimitExceeded() bool {
	if kc.WIPLimit == 0 {
		return false
	}
	return len(kc.Cards) > kc.WIPLimit
}

func (kc *KanbanColumn) GetCardCount() int {
	return len(kc.Cards)
}
