package data

import (
	"time"
)

// 🎯 NAJBARDZIEJ ROZBUDOWANY PROFIL KLIENTA 360°
// Zbiera wszystkie informacje w jedno miejsce - transkrypcje, emaile, serwisy, finanse, AI insights

// CustomerProfile360 - Kompleksowy profil klienta z wszystkimi danymi
type CustomerProfile360 struct {
	ID int64 `gorm:"primaryKey;autoIncrement" json:"id"`

	// === PODSTAWOWE DANE ===
	CustomerID   int64  `gorm:"not null;index" json:"customer_id"`
	FirstName    string `json:"first_name"`
	LastName     string `json:"last_name"`
	CompanyName  string `json:"company_name"`
	CustomerType string `gorm:"default:individual" json:"customer_type"` // individual, business, enterprise

	// === DANE KONTAKTOWE ===
	PrimaryPhone    string      `json:"primary_phone"`
	SecondaryPhones StringArray `gorm:"type:text[]" json:"secondary_phones"`
	PrimaryEmail    string      `json:"primary_email"`
	SecondaryEmails StringArray `gorm:"type:text[]" json:"secondary_emails"`

	// === ADRESY ===
	PrimaryAddress   JSONMap `gorm:"type:jsonb" json:"primary_address"`
	ServiceAddresses JSONMap `gorm:"type:jsonb" json:"service_addresses"`
	BillingAddress   JSONMap `gorm:"type:jsonb" json:"billing_address"`

	// === DANE FIRMOWE ===
	NIP         string `json:"nip"`
	REGON       string `json:"regon"`
	KRS         string `json:"krs"`
	Industry    string `json:"industry"`
	CompanySize string `json:"company_size"`

	// === PREFERENCJE KOMUNIKACJI ===
	PreferredContactMethod string `gorm:"default:phone" json:"preferred_contact_method"`
	PreferredContactTime   string `json:"preferred_contact_time"`
	Language               string `gorm:"default:pl" json:"language"`
	CommunicationFreq      string `json:"communication_frequency"`

	// === SEGMENTACJA & SCORING ===
	CustomerSegment string `json:"customer_segment"` // vip, regular, new, at_risk
	LeadScore       int    `gorm:"default:0" json:"lead_score"`
	CustomerValue   string `json:"customer_value"` // high, medium, low
	LoyaltyLevel    string `json:"loyalty_level"`

	// === DANE FINANSOWE ===
	TotalRevenue      float64    `gorm:"type:decimal(12,2);default:0" json:"total_revenue"`
	AverageOrderValue float64    `gorm:"type:decimal(10,2);default:0" json:"average_order_value"`
	LastPurchaseDate  *time.Time `json:"last_purchase_date"`
	PaymentTerms      string     `json:"payment_terms"`
	CreditLimit       float64    `gorm:"type:decimal(10,2);default:0" json:"credit_limit"`

	// === DANE TECHNICZNE ===
	PropertyType    string  `json:"property_type"` // residential, commercial, industrial
	PropertySize    float64 `json:"property_size"`
	HeatingType     string  `json:"heating_type"`
	CoolingType     string  `json:"cooling_type"`
	VentilationType string  `json:"ventilation_type"`

	// === AI INSIGHTS ===
	AICustomerScore float64 `gorm:"type:decimal(3,2);default:0" json:"ai_customer_score"`
	SentimentTrend  string  `json:"sentiment_trend"`  // improving, stable, declining
	EngagementLevel string  `json:"engagement_level"` // high, medium, low
	ChurnRisk       float64 `gorm:"type:decimal(3,2);default:0" json:"churn_risk"`
	NextBestAction  string  `json:"next_best_action"`

	// === STATYSTYKI KOMUNIKACJI ===
	TotalCalls          int        `gorm:"default:0" json:"total_calls"`
	TotalEmails         int        `gorm:"default:0" json:"total_emails"`
	TotalTranscriptions int        `gorm:"default:0" json:"total_transcriptions"`
	LastContactDate     *time.Time `json:"last_contact_date"`
	AvgResponseTime     int        `json:"avg_response_time_hours"`

	// === STATYSTYKI SERWISOWE ===
	TotalServiceOrders  int        `gorm:"default:0" json:"total_service_orders"`
	TotalEquipment      int        `gorm:"default:0" json:"total_equipment"`
	LastServiceDate     *time.Time `json:"last_service_date"`
	NextServiceDue      *time.Time `json:"next_service_due"`
	ServiceSatisfaction float64    `gorm:"type:decimal(3,2);default:0" json:"service_satisfaction"`

	// === METADANE ===
	Tags       StringArray `gorm:"type:text[]" json:"tags"`
	Notes      string      `gorm:"type:text" json:"notes"`
	Source     string      `json:"source"` // website, phone, email, referral
	AssignedTo *int64      `json:"assigned_to"`

	// === TIMESTAMPS ===
	CreatedAt        time.Time  `gorm:"autoCreateTime" json:"created_at"`
	UpdatedAt        time.Time  `gorm:"autoUpdateTime" json:"updated_at"`
	LastEnrichmentAt *time.Time `json:"last_enrichment_at"`

	// === RELACJE ===
	Customer             *Customer                `gorm:"foreignKey:CustomerID;constraint:OnDelete:CASCADE" json:"customer,omitempty"`
	CommunicationHistory []*CommunicationRecord   `gorm:"foreignKey:CustomerProfileID;constraint:OnDelete:CASCADE" json:"communication_history,omitempty"`
	Transcriptions       []*CustomerTranscription `gorm:"foreignKey:CustomerProfileID;constraint:OnDelete:CASCADE" json:"transcriptions,omitempty"`
	Equipment            []*CustomerEquipment     `gorm:"foreignKey:CustomerProfileID;constraint:OnDelete:CASCADE" json:"equipment,omitempty"`
	FinancialRecords     []*CustomerFinancial     `gorm:"foreignKey:CustomerProfileID;constraint:OnDelete:CASCADE" json:"financial_records,omitempty"`
	AIInsights           []*CustomerAIInsight     `gorm:"foreignKey:CustomerProfileID;constraint:OnDelete:CASCADE" json:"ai_insights,omitempty"`
}

// CommunicationRecord - Rekord wszystkich komunikacji z klientem
type CommunicationRecord struct {
	ID                int64 `gorm:"primaryKey;autoIncrement" json:"id"`
	CustomerProfileID int64 `gorm:"not null;index" json:"customer_profile_id"`

	// === PODSTAWOWE DANE ===
	Type      string `gorm:"not null" json:"type"` // call, email, sms, meeting, transcription
	Direction string `json:"direction"`            // inbound, outbound
	Subject   string `json:"subject"`
	Content   string `gorm:"type:text" json:"content"`

	// === DANE KONTAKTOWE ===
	FromPhone string `json:"from_phone"`
	ToPhone   string `json:"to_phone"`
	FromEmail string `json:"from_email"`
	ToEmail   string `json:"to_email"`

	// === METADANE ===
	Duration int    `json:"duration_seconds"`
	FileName string `json:"file_name"`
	FileSize int64  `json:"file_size"`
	FileType string `json:"file_type"`
	FilePath string `json:"file_path"`

	// === AI ANALIZA ===
	SentimentScore float64     `gorm:"type:decimal(3,2)" json:"sentiment_score"`
	Intent         string      `json:"intent"`
	Priority       string      `json:"priority"`
	Category       string      `json:"category"`
	KeyTopics      StringArray `gorm:"type:text[]" json:"key_topics"`
	ActionItems    StringArray `gorm:"type:text[]" json:"action_items"`

	// === STATUS ===
	Status           string     `gorm:"default:processed" json:"status"`
	IsProcessed      bool       `gorm:"default:false" json:"is_processed"`
	RequiresFollowUp bool       `gorm:"default:false" json:"requires_follow_up"`
	FollowUpDate     *time.Time `json:"follow_up_date"`

	// === TIMESTAMPS ===
	CommunicationDate time.Time `gorm:"not null" json:"communication_date"`
	CreatedAt         time.Time `gorm:"autoCreateTime" json:"created_at"`
	UpdatedAt         time.Time `gorm:"autoUpdateTime" json:"updated_at"`

	// === RELACJE ===
	CustomerProfile *CustomerProfile360 `gorm:"foreignKey:CustomerProfileID;constraint:OnDelete:CASCADE" json:"customer_profile,omitempty"`
}

// CustomerTranscription - Transkrypcje rozmów z analizą AI
type CustomerTranscription struct {
	ID                int64 `gorm:"primaryKey;autoIncrement" json:"id"`
	CustomerProfileID int64 `gorm:"not null;index" json:"customer_profile_id"`

	// === DANE PLIKU ===
	OriginalFileName string `gorm:"not null" json:"original_file_name"`
	StoredFileName   string `json:"stored_file_name"`
	FilePath         string `json:"file_path"`
	FileSize         int64  `json:"file_size"`
	Duration         int    `json:"duration_seconds"`
	AudioFormat      string `json:"audio_format"`

	// === TRANSKRYPCJA ===
	TranscriptionText string  `gorm:"type:text" json:"transcription_text"`
	Confidence        float64 `gorm:"type:decimal(3,2)" json:"confidence"`
	Language          string  `gorm:"default:pl" json:"language"`

	// === AI ANALIZA ===
	Summary        string      `gorm:"type:text" json:"summary"`
	KeyPoints      StringArray `gorm:"type:text[]" json:"key_points"`
	SentimentScore float64     `gorm:"type:decimal(3,2)" json:"sentiment_score"`
	EmotionalTone  string      `json:"emotional_tone"`
	Intent         string      `json:"intent"`
	Category       string      `json:"category"`
	Priority       string      `json:"priority"`

	// === DANE TECHNICZNE ===
	TechnicalIssues    StringArray `gorm:"type:text[]" json:"technical_issues"`
	EquipmentMentioned StringArray `gorm:"type:text[]" json:"equipment_mentioned"`
	ServiceRequests    StringArray `gorm:"type:text[]" json:"service_requests"`

	// === BUSINESS INTELLIGENCE ===
	BusinessValue         float64     `gorm:"type:decimal(10,2)" json:"business_value"`
	LeadQuality           string      `json:"lead_quality"`
	ConversionProbability float64     `gorm:"type:decimal(3,2)" json:"conversion_probability"`
	RecommendedActions    StringArray `gorm:"type:text[]" json:"recommended_actions"`

	// === STATUS ===
	ProcessingStatus string  `gorm:"default:pending" json:"processing_status"`
	IsAnalyzed       bool    `gorm:"default:false" json:"is_analyzed"`
	QualityScore     float64 `gorm:"type:decimal(3,2)" json:"quality_score"`

	// === TIMESTAMPS ===
	RecordedAt  *time.Time `json:"recorded_at"`
	ProcessedAt *time.Time `json:"processed_at"`
	CreatedAt   time.Time  `gorm:"autoCreateTime" json:"created_at"`
	UpdatedAt   time.Time  `gorm:"autoUpdateTime" json:"updated_at"`

	// === RELACJE ===
	CustomerProfile *CustomerProfile360 `gorm:"foreignKey:CustomerProfileID;constraint:OnDelete:CASCADE" json:"customer_profile,omitempty"`
}

// CustomerEquipment - Sprzęt i instalacje klienta
type CustomerEquipment struct {
	ID                int64 `gorm:"primaryKey;autoIncrement" json:"id"`
	CustomerProfileID int64 `gorm:"not null;index" json:"customer_profile_id"`

	// === PODSTAWOWE DANE ===
	Name         string `gorm:"not null" json:"name"`
	Type         string `gorm:"not null" json:"type"` // ac, heating, ventilation, heat_pump
	Brand        string `json:"brand"`
	Model        string `json:"model"`
	SerialNumber string `json:"serial_number"`

	// === PARAMETRY TECHNICZNE ===
	Capacity         float64 `json:"capacity"`
	PowerConsumption float64 `json:"power_consumption"`
	EfficiencyRating string  `json:"efficiency_rating"`
	RefrigerantType  string  `json:"refrigerant_type"`

	// === INSTALACJA ===
	InstallationDate    *time.Time `json:"installation_date"`
	InstallationAddress JSONMap    `gorm:"type:jsonb" json:"installation_address"`
	Room                string     `json:"room"`
	Floor               string     `json:"floor"`

	// === GWARANCJA & SERWIS ===
	WarrantyStartDate *time.Time `json:"warranty_start_date"`
	WarrantyEndDate   *time.Time `json:"warranty_end_date"`
	LastServiceDate   *time.Time `json:"last_service_date"`
	NextServiceDue    *time.Time `json:"next_service_due"`
	ServiceInterval   int        `json:"service_interval_months"`

	// === STATUS ===
	Status      string  `gorm:"default:active" json:"status"` // active, inactive, needs_service, replaced
	HealthScore float64 `gorm:"type:decimal(3,2);default:1.0" json:"health_score"`

	// === TIMESTAMPS ===
	CreatedAt time.Time `gorm:"autoCreateTime" json:"created_at"`
	UpdatedAt time.Time `gorm:"autoUpdateTime" json:"updated_at"`

	// === RELACJE ===
	CustomerProfile *CustomerProfile360 `gorm:"foreignKey:CustomerProfileID;constraint:OnDelete:CASCADE" json:"customer_profile,omitempty"`
}

// CustomerFinancial - Dane finansowe klienta
type CustomerFinancial struct {
	ID                int64 `gorm:"primaryKey;autoIncrement" json:"id"`
	CustomerProfileID int64 `gorm:"not null;index" json:"customer_profile_id"`

	// === TRANSAKCJA ===
	Type        string  `gorm:"not null" json:"type"` // invoice, payment, credit, refund
	Amount      float64 `gorm:"type:decimal(10,2);not null" json:"amount"`
	Currency    string  `gorm:"default:PLN" json:"currency"`
	Description string  `json:"description"`

	// === DOKUMENTY ===
	InvoiceNumber string `json:"invoice_number"`
	DocumentPath  string `json:"document_path"`

	// === STATUS ===
	Status   string     `gorm:"default:pending" json:"status"` // pending, paid, overdue, cancelled
	DueDate  *time.Time `json:"due_date"`
	PaidDate *time.Time `json:"paid_date"`

	// === TIMESTAMPS ===
	TransactionDate time.Time `gorm:"not null" json:"transaction_date"`
	CreatedAt       time.Time `gorm:"autoCreateTime" json:"created_at"`
	UpdatedAt       time.Time `gorm:"autoUpdateTime" json:"updated_at"`

	// === RELACJE ===
	CustomerProfile *CustomerProfile360 `gorm:"foreignKey:CustomerProfileID;constraint:OnDelete:CASCADE" json:"customer_profile,omitempty"`
}

// CustomerAIInsight - AI insights i predykcje
type CustomerAIInsight struct {
	ID                int64 `gorm:"primaryKey;autoIncrement" json:"id"`
	CustomerProfileID int64 `gorm:"not null;index" json:"customer_profile_id"`

	// === INSIGHT ===
	Type        string  `gorm:"not null" json:"type"` // prediction, recommendation, alert, pattern
	Title       string  `gorm:"not null" json:"title"`
	Description string  `gorm:"type:text" json:"description"`
	Confidence  float64 `gorm:"type:decimal(3,2)" json:"confidence"`

	// === DANE ===
	Category          string `json:"category"`
	Priority          string `json:"priority"`
	ActionRequired    bool   `gorm:"default:false" json:"action_required"`
	RecommendedAction string `json:"recommended_action"`

	// === METADANE ===
	Source       string `json:"source"` // email_analysis, transcription_analysis, behavior_analysis
	ModelVersion string `json:"model_version"`

	// === TIMESTAMPS ===
	ValidUntil *time.Time `json:"valid_until"`
	CreatedAt  time.Time  `gorm:"autoCreateTime" json:"created_at"`

	// === RELACJE ===
	CustomerProfile *CustomerProfile360 `gorm:"foreignKey:CustomerProfileID;constraint:OnDelete:CASCADE" json:"customer_profile,omitempty"`
}

// Table names
func (CustomerProfile360) TableName() string    { return "customer_profiles_360" }
func (CommunicationRecord) TableName() string   { return "communication_records" }
func (CustomerTranscription) TableName() string { return "customer_transcriptions" }
func (CustomerEquipment) TableName() string     { return "customer_equipment" }
func (CustomerFinancial) TableName() string     { return "customer_financials" }
func (CustomerAIInsight) TableName() string     { return "customer_ai_insights" }
