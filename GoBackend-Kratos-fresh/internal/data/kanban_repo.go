package data

import (
	"context"
	"fmt"
	"time"

	"github.com/go-kratos/kratos/v2/log"
	"gorm.io/gorm"
)

// 🎯 KANBAN REPOSITORY - Advanced workflow management operations

// KanbanRepo provides database operations for Kanban system
type KanbanRepo struct {
	data *Data
	log  *log.Helper
}

// NewKanbanRepo creates a new Kanban repository
func NewKanbanRepo(data *Data, logger log.Logger) *KanbanRepo {
	return &KanbanRepo{
		data: data,
		log:  log.NewHelper(logger),
	}
}

// ==========================================
// BOARD OPERATIONS
// ==========================================

// CreateBoard creates a new Kanban board
func (r *KanbanRepo) CreateBoard(ctx context.Context, board *KanbanBoard) (*KanbanBoard, error) {
	if err := r.data.db.WithContext(ctx).Create(board).Error; err != nil {
		return nil, fmt.Errorf("failed to create kanban board: %w", err)
	}

	r.log.WithContext(ctx).Infof("Created Kanban board: %s (ID: %d)", board.Name, board.ID)
	return board, nil
}

// GetBoard retrieves a board with its columns and cards
func (r *KanbanRepo) GetBoard(ctx context.Context, id int64) (*KanbanBoard, error) {
	var board KanbanBoard

	err := r.data.db.WithContext(ctx).
		Preload("Columns", func(db *gorm.DB) *gorm.DB {
			return db.Order("position ASC")
		}).
		Preload("Columns.Cards", func(db *gorm.DB) *gorm.DB {
			return db.Order("position ASC")
		}).
		First(&board, id).Error

	if err != nil {
		if err == gorm.ErrRecordNotFound {
			return nil, fmt.Errorf("kanban board not found: %d", id)
		}
		return nil, fmt.Errorf("failed to get kanban board: %w", err)
	}

	return &board, nil
}

// ListBoards retrieves all active boards
func (r *KanbanRepo) ListBoards(ctx context.Context, boardType *KanbanBoardType) ([]*KanbanBoard, error) {
	var boards []*KanbanBoard

	query := r.data.db.WithContext(ctx).Where("is_active = ?", true)
	
	if boardType != nil {
		query = query.Where("board_type = ?", *boardType)
	}

	err := query.Order("position ASC, created_at ASC").Find(&boards).Error
	if err != nil {
		return nil, fmt.Errorf("failed to list kanban boards: %w", err)
	}

	return boards, nil
}

// UpdateBoard updates a Kanban board
func (r *KanbanRepo) UpdateBoard(ctx context.Context, board *KanbanBoard) error {
	if err := r.data.db.WithContext(ctx).Save(board).Error; err != nil {
		return fmt.Errorf("failed to update kanban board: %w", err)
	}

	r.log.WithContext(ctx).Infof("Updated Kanban board: %s (ID: %d)", board.Name, board.ID)
	return nil
}

// DeleteBoard soft deletes a board
func (r *KanbanRepo) DeleteBoard(ctx context.Context, id int64) error {
	result := r.data.db.WithContext(ctx).Model(&KanbanBoard{}).
		Where("id = ?", id).
		Update("is_active", false)

	if result.Error != nil {
		return fmt.Errorf("failed to delete kanban board: %w", result.Error)
	}

	if result.RowsAffected == 0 {
		return fmt.Errorf("kanban board not found: %d", id)
	}

	r.log.WithContext(ctx).Infof("Deleted Kanban board: %d", id)
	return nil
}

// ==========================================
// COLUMN OPERATIONS
// ==========================================

// CreateColumn creates a new column in a board
func (r *KanbanRepo) CreateColumn(ctx context.Context, column *KanbanColumn) (*KanbanColumn, error) {
	if err := r.data.db.WithContext(ctx).Create(column).Error; err != nil {
		return nil, fmt.Errorf("failed to create kanban column: %w", err)
	}

	r.log.WithContext(ctx).Infof("Created Kanban column: %s (ID: %d)", column.Name, column.ID)
	return column, nil
}

// UpdateColumn updates a column
func (r *KanbanRepo) UpdateColumn(ctx context.Context, column *KanbanColumn) error {
	if err := r.data.db.WithContext(ctx).Save(column).Error; err != nil {
		return fmt.Errorf("failed to update kanban column: %w", err)
	}

	r.log.WithContext(ctx).Infof("Updated Kanban column: %s (ID: %d)", column.Name, column.ID)
	return nil
}

// DeleteColumn deletes a column and moves its cards to another column
func (r *KanbanRepo) DeleteColumn(ctx context.Context, columnID, targetColumnID int64) error {
	return r.data.db.WithContext(ctx).Transaction(func(tx *gorm.DB) error {
		// Move all cards to target column
		if err := tx.Model(&KanbanCard{}).
			Where("column_id = ?", columnID).
			Update("column_id", targetColumnID).Error; err != nil {
			return fmt.Errorf("failed to move cards: %w", err)
		}

		// Delete the column
		if err := tx.Delete(&KanbanColumn{}, columnID).Error; err != nil {
			return fmt.Errorf("failed to delete column: %w", err)
		}

		return nil
	})
}

// ==========================================
// CARD OPERATIONS
// ==========================================

// CreateCard creates a new Kanban card
func (r *KanbanRepo) CreateCard(ctx context.Context, card *KanbanCard) (*KanbanCard, error) {
	// Set position to end of column if not specified
	if card.Position == 0 {
		var maxPosition int
		r.data.db.WithContext(ctx).Model(&KanbanCard{}).
			Where("column_id = ?", card.ColumnID).
			Select("COALESCE(MAX(position), 0)").
			Scan(&maxPosition)
		card.Position = maxPosition + 1
	}

	if err := r.data.db.WithContext(ctx).Create(card).Error; err != nil {
		return nil, fmt.Errorf("failed to create kanban card: %w", err)
	}

	// Create activity record
	activity := &KanbanActivity{
		CardID:       card.ID,
		ActivityType: "created",
		Description:  fmt.Sprintf("Card '%s' was created", card.Title),
		CreatedBy:    card.CreatedBy,
	}
	r.CreateActivity(ctx, activity)

	r.log.WithContext(ctx).Infof("Created Kanban card: %s (ID: %d)", card.Title, card.ID)
	return card, nil
}

// GetCard retrieves a card with all its relationships
func (r *KanbanRepo) GetCard(ctx context.Context, id int64) (*KanbanCard, error) {
	var card KanbanCard

	err := r.data.db.WithContext(ctx).
		Preload("Board").
		Preload("Column").
		Preload("Activities", func(db *gorm.DB) *gorm.DB {
			return db.Order("created_at DESC")
		}).
		Preload("Comments", func(db *gorm.DB) *gorm.DB {
			return db.Order("created_at ASC")
		}).
		Preload("Attachments").
		First(&card, id).Error

	if err != nil {
		if err == gorm.ErrRecordNotFound {
			return nil, fmt.Errorf("kanban card not found: %d", id)
		}
		return nil, fmt.Errorf("failed to get kanban card: %w", err)
	}

	return &card, nil
}

// UpdateCard updates a Kanban card
func (r *KanbanRepo) UpdateCard(ctx context.Context, card *KanbanCard) error {
	if err := r.data.db.WithContext(ctx).Save(card).Error; err != nil {
		return fmt.Errorf("failed to update kanban card: %w", err)
	}

	r.log.WithContext(ctx).Infof("Updated Kanban card: %s (ID: %d)", card.Title, card.ID)
	return nil
}

// MoveCard moves a card to a different column and position
func (r *KanbanRepo) MoveCard(ctx context.Context, cardID, newColumnID int64, newPosition int, userID int64) error {
	return r.data.db.WithContext(ctx).Transaction(func(tx *gorm.DB) error {
		// Get current card
		var card KanbanCard
		if err := tx.First(&card, cardID).Error; err != nil {
			return fmt.Errorf("card not found: %w", err)
		}

		oldColumnID := card.ColumnID
		oldPosition := card.Position

		// Update card position and column
		card.ColumnID = newColumnID
		card.Position = newPosition
		if err := tx.Save(&card).Error; err != nil {
			return fmt.Errorf("failed to move card: %w", err)
		}

		// Create activity record
		var oldColumn, newColumn KanbanColumn
		tx.First(&oldColumn, oldColumnID)
		tx.First(&newColumn, newColumnID)

		activity := &KanbanActivity{
			CardID:       cardID,
			ActivityType: "moved",
			Description:  fmt.Sprintf("Card moved from '%s' to '%s'", oldColumn.Name, newColumn.Name),
			OldValue:     fmt.Sprintf("%s (position %d)", oldColumn.Name, oldPosition),
			NewValue:     fmt.Sprintf("%s (position %d)", newColumn.Name, newPosition),
			CreatedBy:    userID,
		}
		if err := tx.Create(activity).Error; err != nil {
			return fmt.Errorf("failed to create activity: %w", err)
		}

		return nil
	})
}

// DeleteCard deletes a Kanban card
func (r *KanbanRepo) DeleteCard(ctx context.Context, id int64) error {
	result := r.data.db.WithContext(ctx).Delete(&KanbanCard{}, id)
	if result.Error != nil {
		return fmt.Errorf("failed to delete kanban card: %w", result.Error)
	}

	if result.RowsAffected == 0 {
		return fmt.Errorf("kanban card not found: %d", id)
	}

	r.log.WithContext(ctx).Infof("Deleted Kanban card: %d", id)
	return nil
}

// ==========================================
// ACTIVITY OPERATIONS
// ==========================================

// CreateActivity creates a new activity record
func (r *KanbanRepo) CreateActivity(ctx context.Context, activity *KanbanActivity) error {
	if err := r.data.db.WithContext(ctx).Create(activity).Error; err != nil {
		return fmt.Errorf("failed to create kanban activity: %w", err)
	}
	return nil
}

// GetCardActivities retrieves activities for a card
func (r *KanbanRepo) GetCardActivities(ctx context.Context, cardID int64) ([]*KanbanActivity, error) {
	var activities []*KanbanActivity

	err := r.data.db.WithContext(ctx).
		Where("card_id = ?", cardID).
		Order("created_at DESC").
		Find(&activities).Error

	if err != nil {
		return nil, fmt.Errorf("failed to get card activities: %w", err)
	}

	return activities, nil
}

// ==========================================
// COMMENT OPERATIONS
// ==========================================

// CreateComment creates a new comment on a card
func (r *KanbanRepo) CreateComment(ctx context.Context, comment *KanbanComment) (*KanbanComment, error) {
	if err := r.data.db.WithContext(ctx).Create(comment).Error; err != nil {
		return nil, fmt.Errorf("failed to create kanban comment: %w", err)
	}

	// Create activity record
	activity := &KanbanActivity{
		CardID:       comment.CardID,
		ActivityType: "commented",
		Description:  "Added a comment",
		CreatedBy:    comment.CreatedBy,
	}
	r.CreateActivity(ctx, activity)

	return comment, nil
}

// ==========================================
// ANALYTICS OPERATIONS
// ==========================================

// GetBoardAnalytics retrieves analytics data for a board
func (r *KanbanRepo) GetBoardAnalytics(ctx context.Context, boardID int64) (map[string]interface{}, error) {
	analytics := make(map[string]interface{})

	// Total cards
	var totalCards int64
	r.data.db.WithContext(ctx).Model(&KanbanCard{}).Where("board_id = ?", boardID).Count(&totalCards)
	analytics["total_cards"] = totalCards

	// Cards by column
	var columnStats []struct {
		ColumnID   int64  `json:"column_id"`
		ColumnName string `json:"column_name"`
		CardCount  int64  `json:"card_count"`
	}
	
	r.data.db.WithContext(ctx).
		Table("kanban_cards").
		Select("kanban_columns.id as column_id, kanban_columns.name as column_name, COUNT(kanban_cards.id) as card_count").
		Joins("JOIN kanban_columns ON kanban_cards.column_id = kanban_columns.id").
		Where("kanban_cards.board_id = ?", boardID).
		Group("kanban_columns.id, kanban_columns.name").
		Scan(&columnStats)
	
	analytics["column_stats"] = columnStats

	// Cards by priority
	var priorityStats []struct {
		Priority  string `json:"priority"`
		CardCount int64  `json:"card_count"`
	}
	
	r.data.db.WithContext(ctx).
		Model(&KanbanCard{}).
		Select("priority, COUNT(*) as card_count").
		Where("board_id = ?", boardID).
		Group("priority").
		Scan(&priorityStats)
	
	analytics["priority_stats"] = priorityStats

	// Overdue cards
	var overdueCards int64
	r.data.db.WithContext(ctx).
		Model(&KanbanCard{}).
		Where("board_id = ? AND due_date < ? AND completed_at IS NULL", boardID, time.Now()).
		Count(&overdueCards)
	analytics["overdue_cards"] = overdueCards

	return analytics, nil
}
