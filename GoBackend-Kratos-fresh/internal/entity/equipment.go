package entity

import (
	"encoding/json"
	"time"
)

// 🏭 Equipment Entity - Core Equipment Management
// GoBackend-Kratos HVAC CRM System

// Equipment represents a piece of HVAC equipment
type Equipment struct {
	ID               int64           `json:"id" gorm:"primaryKey;autoIncrement"`
	CustomerID       int64           `json:"customer_id" gorm:"not null;index"`
	Name             string          `json:"name" gorm:"not null;size:255"`
	Type             string          `json:"type" gorm:"not null;size:100;index"` // hvac_unit, heat_pump, air_conditioner, furnace
	Brand            string          `json:"brand" gorm:"size:100"`
	Model            string          `json:"model" gorm:"size:100"`
	SerialNumber     string          `json:"serial_number" gorm:"uniqueIndex;size:100"`
	InstallationDate *time.Time      `json:"installation_date"`
	WarrantyExpiry   *time.Time      `json:"warranty_expiry"`
	Location         string          `json:"location" gorm:"size:500"`
	Latitude         *float64        `json:"latitude"`
	Longitude        *float64        `json:"longitude"`
	Status           EquipmentStatus `json:"status" gorm:"default:'active'"`
	HealthScore      float64         `json:"health_score" gorm:"default:1.0"` // 0.0 to 1.0
	HealthStatus     string          `json:"health_status" gorm:"default:'excellent'"`
	Specifications   json.RawMessage `json:"specifications" gorm:"type:jsonb"`
	Metadata         json.RawMessage `json:"metadata" gorm:"type:jsonb"`
	CreatedAt        time.Time       `json:"created_at" gorm:"autoCreateTime"`
	UpdatedAt        time.Time       `json:"updated_at" gorm:"autoUpdateTime"`

	// Relationships - Note: Customer relationship handled at service layer to avoid circular imports
	HealthMetrics      []HealthMetric      `json:"health_metrics,omitempty" gorm:"foreignKey:EquipmentID"`
	MaintenanceRecords []MaintenanceRecord `json:"maintenance_records,omitempty" gorm:"foreignKey:EquipmentID"`
	Parts              []EquipmentPart     `json:"parts,omitempty" gorm:"foreignKey:EquipmentID"`
}

// EquipmentStatus represents the operational status of equipment
type EquipmentStatus string

const (
	EquipmentStatusActive      EquipmentStatus = "active"
	EquipmentStatusInactive    EquipmentStatus = "inactive"
	EquipmentStatusMaintenance EquipmentStatus = "maintenance"
	EquipmentStatusRetired     EquipmentStatus = "retired"
	EquipmentStatusFaulty      EquipmentStatus = "faulty"
)

// HealthMetric represents a health measurement for equipment
type HealthMetric struct {
	ID           int64     `json:"id" gorm:"primaryKey;autoIncrement"`
	EquipmentID  int64     `json:"equipment_id" gorm:"not null;index"`
	Name         string    `json:"name" gorm:"not null;size:100"`
	Value        float64   `json:"value"`
	Unit         string    `json:"unit" gorm:"size:50"`
	ThresholdMin *float64  `json:"threshold_min"`
	ThresholdMax *float64  `json:"threshold_max"`
	Status       string    `json:"status" gorm:"size:50"` // normal, warning, critical
	RecordedAt   time.Time `json:"recorded_at" gorm:"autoCreateTime"`
	CreatedAt    time.Time `json:"created_at" gorm:"autoCreateTime"`

	// Relationships
	Equipment *Equipment `json:"equipment,omitempty" gorm:"foreignKey:EquipmentID"`
}

// MaintenanceRecord represents a maintenance activity
type MaintenanceRecord struct {
	ID                    int64           `json:"id" gorm:"primaryKey;autoIncrement"`
	EquipmentID           int64           `json:"equipment_id" gorm:"not null;index"`
	MaintenanceType       string          `json:"maintenance_type" gorm:"not null;size:50"` // preventive, corrective, emergency
	PerformedDate         time.Time       `json:"performed_date"`
	Description           string          `json:"description" gorm:"type:text"`
	TechnicianID          *int64          `json:"technician_id" gorm:"index"`
	PartsUsed             json.RawMessage `json:"parts_used" gorm:"type:jsonb"`
	ActualDurationMinutes int32           `json:"actual_duration_minutes"`
	Status                string          `json:"status" gorm:"size:50"` // scheduled, in_progress, completed, cancelled
	Notes                 string          `json:"notes" gorm:"type:text"`
	Cost                  float64         `json:"cost" gorm:"default:0"`
	CreatedAt             time.Time       `json:"created_at" gorm:"autoCreateTime"`
	UpdatedAt             time.Time       `json:"updated_at" gorm:"autoUpdateTime"`

	// Relationships
	Equipment *Equipment `json:"equipment,omitempty" gorm:"foreignKey:EquipmentID"`
}

// MaintenanceSchedule represents scheduled maintenance
type MaintenanceSchedule struct {
	ID                       int64           `json:"id" gorm:"primaryKey;autoIncrement"`
	EquipmentID              int64           `json:"equipment_id" gorm:"not null;index"`
	MaintenanceType          string          `json:"maintenance_type" gorm:"not null;size:50"`
	ScheduledDate            time.Time       `json:"scheduled_date"`
	Description              string          `json:"description" gorm:"type:text"`
	TechnicianID             *int64          `json:"technician_id" gorm:"index"`
	RequiredParts            json.RawMessage `json:"required_parts" gorm:"type:jsonb"`
	EstimatedDurationMinutes int32           `json:"estimated_duration_minutes"`
	Status                   string          `json:"status" gorm:"size:50;default:'scheduled'"`
	CreatedAt                time.Time       `json:"created_at" gorm:"autoCreateTime"`
	UpdatedAt                time.Time       `json:"updated_at" gorm:"autoUpdateTime"`

	// Relationships
	Equipment *Equipment `json:"equipment,omitempty" gorm:"foreignKey:EquipmentID"`
}

// EquipmentPart represents a part/component of equipment
type EquipmentPart struct {
	ID               int64           `json:"id" gorm:"primaryKey;autoIncrement"`
	EquipmentID      int64           `json:"equipment_id" gorm:"not null;index"`
	Name             string          `json:"name" gorm:"not null;size:255"`
	PartNumber       string          `json:"part_number" gorm:"size:100"`
	Manufacturer     string          `json:"manufacturer" gorm:"size:100"`
	InstallationDate *time.Time      `json:"installation_date"`
	WarrantyExpiry   *time.Time      `json:"warranty_expiry"`
	Status           string          `json:"status" gorm:"size:50;default:'new'"` // new, good, worn, needs_replacement, replaced
	Cost             float64         `json:"cost" gorm:"default:0"`
	Supplier         string          `json:"supplier" gorm:"size:255"`
	Specifications   json.RawMessage `json:"specifications" gorm:"type:jsonb"`
	CreatedAt        time.Time       `json:"created_at" gorm:"autoCreateTime"`
	UpdatedAt        time.Time       `json:"updated_at" gorm:"autoUpdateTime"`

	// Relationships
	Equipment *Equipment `json:"equipment,omitempty" gorm:"foreignKey:EquipmentID"`
}

// EquipmentAnalytics represents analytics data for equipment
type EquipmentAnalytics struct {
	EquipmentID           int64               `json:"equipment_id"`
	UptimePercentage      float64             `json:"uptime_percentage"`
	MaintenanceCount      int32               `json:"maintenance_count"`
	TotalMaintenanceCost  float64             `json:"total_maintenance_cost"`
	EnergyEfficiencyScore float64             `json:"energy_efficiency_score"`
	PerformanceMetrics    []PerformanceMetric `json:"performance_metrics"`
	AnalysisPeriodStart   time.Time           `json:"analysis_period_start"`
	AnalysisPeriodEnd     time.Time           `json:"analysis_period_end"`
}

// PerformanceMetric represents a performance measurement
type PerformanceMetric struct {
	Name      string  `json:"name"`
	Value     float64 `json:"value"`
	Unit      string  `json:"unit"`
	Benchmark float64 `json:"benchmark"`
	Trend     string  `json:"trend"` // improving, stable, declining
}

// FleetOverview represents an overview of equipment fleet
type FleetOverview struct {
	TotalEquipment     int32                  `json:"total_equipment"`
	ActiveEquipment    int32                  `json:"active_equipment"`
	MaintenanceDue     int32                  `json:"maintenance_due"`
	CriticalHealth     int32                  `json:"critical_health"`
	AverageHealthScore float64                `json:"average_health_score"`
	TotalFleetValue    float64                `json:"total_fleet_value"`
	TypeSummaries      []EquipmentTypeSummary `json:"type_summaries"`
}

// EquipmentTypeSummary represents summary data for equipment type
type EquipmentTypeSummary struct {
	EquipmentType      string  `json:"equipment_type"`
	Count              int32   `json:"count"`
	AverageHealthScore float64 `json:"average_health_score"`
	MaintenanceDue     int32   `json:"maintenance_due"`
}

// TableName returns the table name for Equipment
func (Equipment) TableName() string {
	return "equipment"
}

// TableName returns the table name for HealthMetric
func (HealthMetric) TableName() string {
	return "health_metrics"
}

// TableName returns the table name for MaintenanceRecord
func (MaintenanceRecord) TableName() string {
	return "maintenance_records"
}

// TableName returns the table name for MaintenanceSchedule
func (MaintenanceSchedule) TableName() string {
	return "maintenance_schedules"
}

// TableName returns the table name for EquipmentPart
func (EquipmentPart) TableName() string {
	return "equipment_parts"
}

// IsHealthy returns true if equipment health is good
func (e *Equipment) IsHealthy() bool {
	return e.HealthScore >= 0.7 && e.HealthStatus != "critical"
}

// NeedsMaintenanceSoon returns true if maintenance is due soon
func (e *Equipment) NeedsMaintenanceSoon() bool {
	// This would be enhanced with actual maintenance scheduling logic
	return e.HealthScore < 0.5
}

// GetAge returns the age of equipment in years
func (e *Equipment) GetAge() float64 {
	if e.InstallationDate == nil {
		return 0
	}
	return time.Since(*e.InstallationDate).Hours() / (24 * 365)
}

// IsUnderWarranty returns true if equipment is still under warranty
func (e *Equipment) IsUnderWarranty() bool {
	if e.WarrantyExpiry == nil {
		return false
	}
	return time.Now().Before(*e.WarrantyExpiry)
}
