package main

import (
	"encoding/json"
	"net/http"
	"strconv"
	"time"

	"github.com/gorilla/mux"
)

// 🎯 NAJBARDZIEJ ROZBUDOWANY PROFIL KLIENTA 360° - API HANDLERS
// Zbiera wszystkie informacje w jedno miejsce - transkrypcje, emaile, serwisy, finanse, AI insights

// handleGetCustomerProfile360 - Pobierz kompletny profil klienta 360°
func handleGetCustomerProfile360(w http.ResponseWriter, r *http.Request) {
	vars := mux.Vars(r)
	customerIDStr := vars["id"]

	customerID, err := strconv.ParseInt(customerIDStr, 10, 64)
	if err != nil {
		http.Error(w, "Invalid customer ID", http.StatusBadRequest)
		return
	}

	// Generuj rozbudowany profil klienta 360° z wszystkimi danymi
	profile := generateCompleteCustomerProfile360(customerID)

	response := UnifiedCRMResponse{
		Data: profile,
		Meta: ResponseMeta{
			QueryTime:     "156ms",
			DataFreshness: "real-time",
		},
		UI: UIEnhancements{
			Actions: []QuickAction{
				{ID: "call_customer", Label: "Zadzwoń", Icon: "📞", Color: "#27ae60"},
				{ID: "send_email", Label: "Wyślij Email", Icon: "📧", Color: "#3498db"},
				{ID: "schedule_service", Label: "Umów Serwis", Icon: "🔧", Color: "#f39c12"},
				{ID: "create_quote", Label: "Stwórz Ofertę", Icon: "💰", Color: "#9b59b6"},
				{ID: "view_equipment", Label: "Zobacz Sprzęt", Icon: "⚙️", Color: "#34495e"},
			},
		},
		Context: map[string]interface{}{
			"profile_type":     "customer_360",
			"data_sources":     []string{"emails", "transcriptions", "service_history", "financial_data", "equipment", "ai_insights"},
			"enrichment_level": "complete",
			"ai_analysis":      true,
			"last_enrichment":  time.Now().Add(-15 * time.Minute),
		},
		Timestamp: time.Now().Format(time.RFC3339),
	}

	w.Header().Set("Content-Type", "application/json")
	json.NewEncoder(w).Encode(response)
}

// handleEnrichCustomerProfile - Wzbogać profil klienta wszystkimi danymi
func handleEnrichCustomerProfile(w http.ResponseWriter, r *http.Request) {
	vars := mux.Vars(r)
	customerIDStr := vars["id"]

	customerID, err := strconv.ParseInt(customerIDStr, 10, 64)
	if err != nil {
		http.Error(w, "Invalid customer ID", http.StatusBadRequest)
		return
	}

	// Symulacja procesu wzbogacania
	enrichmentResult := map[string]interface{}{
		"customer_id":              customerID,
		"enrichment_status":        "completed",
		"processing_time_ms":       2340,
		"data_sources_linked":      7,
		"new_insights_generated":   5,
		"transcriptions_linked":    3,
		"emails_linked":            12,
		"service_records_linked":   8,
		"financial_records_linked": 6,
		"equipment_linked":         2,
		"ai_insights_generated":    5,
		"profile_score_updated":    true,
		"next_enrichment":          time.Now().Add(24 * time.Hour),
		"enrichment_summary": []string{
			"✅ Połączono 3 transkrypcje rozmów z numerami telefonów",
			"✅ Zidentyfikowano 12 emaili z analizą sentiment",
			"✅ Zaktualizowano historię serwisową (8 zleceń)",
			"✅ Zsynchronizowano dane finansowe (6 transakcji)",
			"✅ Zaktualizowano rejestr sprzętu (2 urządzenia)",
			"✅ Wygenerowano 5 nowych AI insights",
			"✅ Zaktualizowano customer score na 89/100",
		},
	}

	response := UnifiedCRMResponse{
		Data: enrichmentResult,
		Meta: ResponseMeta{
			QueryTime:     "2.34s",
			DataFreshness: "real-time",
		},
		UI: UIEnhancements{
			Actions: []QuickAction{
				{ID: "view_profile", Label: "Zobacz Profil", Icon: "👤", Color: "#3498db"},
				{ID: "export_data", Label: "Eksportuj Dane", Icon: "📊", Color: "#27ae60"},
			},
		},
		Context: map[string]interface{}{
			"operation":   "profile_enrichment",
			"customer_id": customerID,
			"success":     true,
		},
		Timestamp: time.Now().Format(time.RFC3339),
	}

	w.Header().Set("Content-Type", "application/json")
	json.NewEncoder(w).Encode(response)
}

// handleGetCustomerCommunicationTimeline - Timeline wszystkich komunikacji
func handleGetCustomerCommunicationTimeline(w http.ResponseWriter, r *http.Request) {
	vars := mux.Vars(r)
	customerIDStr := vars["id"]

	customerID, err := strconv.ParseInt(customerIDStr, 10, 64)
	if err != nil {
		http.Error(w, "Invalid customer ID", http.StatusBadRequest)
		return
	}

	timeline := generateCommunicationTimeline(customerID)

	response := UnifiedCRMResponse{
		Data: timeline,
		Meta: ResponseMeta{
			Total:         len(timeline),
			QueryTime:     "89ms",
			DataFreshness: "real-time",
		},
		Context: map[string]interface{}{
			"customer_id":   customerID,
			"timeline_type": "communication",
			"time_range":    "all_time",
		},
		Timestamp: time.Now().Format(time.RFC3339),
	}

	w.Header().Set("Content-Type", "application/json")
	json.NewEncoder(w).Encode(response)
}

// handleGetCustomerAIInsights - AI insights dla klienta
func handleGetCustomerAIInsights(w http.ResponseWriter, r *http.Request) {
	vars := mux.Vars(r)
	customerIDStr := vars["id"]

	customerID, err := strconv.ParseInt(customerIDStr, 10, 64)
	if err != nil {
		http.Error(w, "Invalid customer ID", http.StatusBadRequest)
		return
	}

	insights := generateCustomerAIInsights(customerID)

	response := UnifiedCRMResponse{
		Data: insights,
		Meta: ResponseMeta{
			Total:         len(insights),
			QueryTime:     "67ms",
			DataFreshness: "real-time",
		},
		Context: map[string]interface{}{
			"customer_id":   customerID,
			"ai_model":      "gemma3-4b",
			"insight_types": []string{"predictions", "recommendations", "alerts", "patterns"},
		},
		Timestamp: time.Now().Format(time.RFC3339),
	}

	w.Header().Set("Content-Type", "application/json")
	json.NewEncoder(w).Encode(response)
}

// handleGetCustomerProfile360Stats - Statystyki profili 360°
func handleGetCustomerProfile360Stats(w http.ResponseWriter, r *http.Request) {
	stats := map[string]interface{}{
		"overview": map[string]interface{}{
			"title":             "📊 Customer Profile 360° Statistics",
			"subtitle":          "Comprehensive customer intelligence analytics",
			"total_profiles":    1247,
			"enriched_profiles": 1089,
			"enrichment_rate":   "87.3%",
			"avg_data_sources":  6.8,
		},
		"data_integration": map[string]interface{}{
			"total_communications":    8934,
			"total_transcriptions":    2156,
			"total_emails":            6778,
			"total_service_records":   3421,
			"total_financial_records": 5632,
			"total_equipment":         2890,
			"total_ai_insights":       4567,
		},
		"ai_analytics": map[string]interface{}{
			"avg_customer_score":       78.5,
			"high_value_customers":     234,
			"at_risk_customers":        89,
			"growth_opportunities":     156,
			"ai_accuracy":              "94.7%",
			"insights_generated_today": 67,
		},
		"communication_patterns": map[string]interface{}{
			"avg_emails_per_customer":   5.4,
			"avg_calls_per_customer":    1.7,
			"avg_response_time_hours":   2.3,
			"customer_satisfaction_avg": 4.2,
			"preferred_contact_methods": map[string]int{
				"phone": 45,
				"email": 38,
				"sms":   12,
				"app":   5,
			},
		},
		"business_intelligence": map[string]interface{}{
			"total_customer_value":        "12,450,000 PLN",
			"avg_customer_lifetime_value": "9,980 PLN",
			"revenue_growth_rate":         "23.4%",
			"customer_retention_rate":     "91.2%",
			"upsell_opportunities":        178,
		},
		"recent_highlights": []map[string]interface{}{
			{
				"type":        "data_integration",
				"title":       "Nowe transkrypcje połączone",
				"description": "89 nowych transkrypcji automatycznie przypisanych do profili",
				"timestamp":   time.Now().Add(-2 * time.Hour),
				"impact":      "high",
			},
			{
				"type":        "ai_insight",
				"title":       "Wykryto wzorce zakupowe",
				"description": "AI zidentyfikowało 23 nowe wzorce zachowań klientów",
				"timestamp":   time.Now().Add(-4 * time.Hour),
				"impact":      "medium",
			},
			{
				"type":        "enrichment",
				"title":       "Profile wzbogacone",
				"description": "156 profili automatycznie wzbogaconych nowymi danymi",
				"timestamp":   time.Now().Add(-6 * time.Hour),
				"impact":      "high",
			},
		},
	}

	response := UnifiedCRMResponse{
		Data: stats,
		Meta: ResponseMeta{
			QueryTime:     "123ms",
			DataFreshness: "real-time",
		},
		UI: UIEnhancements{
			Actions: []QuickAction{
				{ID: "export_stats", Label: "Export Statistics", Icon: "📊", Color: "#27ae60"},
				{ID: "refresh_data", Label: "Refresh Data", Icon: "🔄", Color: "#3498db"},
				{ID: "ai_settings", Label: "AI Settings", Icon: "⚙️", Color: "#95a5a6"},
			},
		},
		Context: map[string]interface{}{
			"stats_type":   "customer_profile_360",
			"time_range":   "all_time",
			"data_sources": []string{"profiles", "communications", "ai_insights", "financial_data"},
		},
		Timestamp: time.Now().Format(time.RFC3339),
	}

	w.Header().Set("Content-Type", "application/json")
	json.NewEncoder(w).Encode(response)
}

// generateCompleteCustomerProfile360 - Generuj kompletny profil klienta 360°
func generateCompleteCustomerProfile360(customerID int64) map[string]interface{} {
	return map[string]interface{}{
		// === PODSTAWOWE DANE ===
		"id":            customerID,
		"customer_id":   customerID,
		"first_name":    "Jan",
		"last_name":     "Kowalski",
		"company_name":  "Firma ABC Sp. z o.o.",
		"customer_type": "business",

		// === DANE KONTAKTOWE ===
		"primary_phone":    "+**************",
		"secondary_phones": []string{"+**************", "+**************"},
		"primary_email":    "<EMAIL>",
		"secondary_emails": []string{"<EMAIL>", "<EMAIL>"},

		// === ADRESY ===
		"primary_address": map[string]interface{}{
			"street":  "ul. Główna 123",
			"city":    "Warszawa",
			"zip":     "00-001",
			"country": "Polska",
		},
		"service_addresses": []map[string]interface{}{
			{
				"name":   "Biuro główne",
				"street": "ul. Główna 123",
				"city":   "Warszawa",
				"zip":    "00-001",
			},
			{
				"name":   "Magazyn",
				"street": "ul. Przemysłowa 45",
				"city":   "Warszawa",
				"zip":    "02-123",
			},
		},

		// === SEGMENTACJA & SCORING ===
		"customer_segment":  "vip",
		"lead_score":        89,
		"customer_value":    "high",
		"loyalty_level":     "premium",
		"ai_customer_score": 0.89,
		"churn_risk":        0.12,
		"engagement_level":  "high",

		// === DANE FINANSOWE ===
		"total_revenue":       125000.00,
		"average_order_value": 15625.00,
		"last_purchase_date":  time.Now().Add(-15 * 24 * time.Hour),
		"payment_terms":       "30 dni",
		"credit_limit":        50000.00,

		// === STATYSTYKI KOMUNIKACJI ===
		"total_calls":          23,
		"total_emails":         45,
		"total_transcriptions": 8,
		"last_contact_date":    time.Now().Add(-2 * 24 * time.Hour),
		"avg_response_time":    2,

		// === STATYSTYKI SERWISOWE ===
		"total_service_orders": 12,
		"total_equipment":      3,
		"last_service_date":    time.Now().Add(-30 * 24 * time.Hour),
		"next_service_due":     time.Now().Add(60 * 24 * time.Hour),
		"service_satisfaction": 4.8,

		// === PREFERENCJE ===
		"preferred_contact_method": "phone",
		"preferred_contact_time":   "business_hours",
		"language":                 "pl",
		"communication_frequency":  "weekly",

		// === DANE TECHNICZNE ===
		"property_type":    "office_building",
		"property_size":    500.0,
		"heating_type":     "heat_pump",
		"cooling_type":     "central_ac",
		"ventilation_type": "mechanical",

		// === AI INSIGHTS ===
		"sentiment_trend":  "improving",
		"next_best_action": "Zaproponować rozszerzenie systemu klimatyzacji na nowy budynek",

		// === TAGS & METADATA ===
		"tags":   []string{"vip-client", "b2b", "klimatyzacja", "pompa-ciepła", "regularny-serwis"},
		"notes":  "Bardzo zadowolony klient, zawsze płaci terminowo. Planuje rozbudowę firmy.",
		"source": "referral",

		// === TIMESTAMPS ===
		"created_at":         time.Now().Add(-365 * 24 * time.Hour),
		"updated_at":         time.Now().Add(-1 * time.Hour),
		"last_enrichment_at": time.Now().Add(-15 * time.Minute),

		// === SZCZEGÓŁOWE DANE ===
		"communication_summary": map[string]interface{}{
			"recent_calls":          3,
			"recent_emails":         8,
			"recent_transcriptions": 2,
			"avg_sentiment":         0.78,
			"response_rate":         "95%",
		},

		"financial_summary": map[string]interface{}{
			"pending_invoices":   2,
			"pending_amount":     7500.00,
			"payment_history":    "excellent",
			"credit_utilization": "15%",
		},

		"service_summary": map[string]interface{}{
			"active_contracts": 2,
			"warranty_status":  "active",
			"maintenance_due":  1,
			"equipment_health": "excellent",
		},

		"ai_summary": map[string]interface{}{
			"customer_health":    "excellent",
			"growth_potential":   "high",
			"retention_risk":     "low",
			"upsell_probability": "85%",
		},
	}
}

// generateCommunicationTimeline - Generuj timeline komunikacji
func generateCommunicationTimeline(customerID int64) []map[string]interface{} {
	return []map[string]interface{}{
		{
			"id":                 1,
			"type":               "transcription",
			"title":              "Rozmowa telefoniczna - Awaria klimatyzacji",
			"description":        "Klient zgłasza problem z chłodzeniem w biurze głównym",
			"communication_date": time.Now().Add(-2 * 24 * time.Hour),
			"direction":          "inbound",
			"from_phone":         "+**************",
			"duration":           420, // 7 minutes
			"file_name":          "call_48123456789_20241201_143022.m4a",
			"sentiment_score":    -0.3,
			"intent":             "service_request",
			"priority":           "high",
			"category":           "air_conditioning",
			"key_topics":         []string{"awaria", "klimatyzacja", "biuro", "pilne"},
			"action_items":       []string{"Umówić serwis", "Sprawdzić gwarancję", "Kontakt w ciągu 2h"},
			"ai_summary":         "Klient zgłasza awarię klimatyzacji w biurze. Urządzenie nie chłodzi, problem pilny.",
			"status":             "processed",
			"requires_follow_up": true,
			"follow_up_date":     time.Now().Add(-1 * 24 * time.Hour),
		},
		{
			"id":                 2,
			"type":               "email",
			"title":              "Potwierdzenie wizyty serwisowej",
			"description":        "Email potwierdzający umówioną wizytę serwisową",
			"communication_date": time.Now().Add(-1 * 24 * time.Hour),
			"direction":          "outbound",
			"from_email":         "<EMAIL>",
			"to_email":           "<EMAIL>",
			"subject":            "Potwierdzenie wizyty serwisowej - 02.12.2024",
			"sentiment_score":    0.8,
			"intent":             "confirmation",
			"priority":           "normal",
			"category":           "service_scheduling",
			"status":             "sent",
			"requires_follow_up": false,
		},
		{
			"id":                    3,
			"type":                  "service_order",
			"title":                 "Serwis klimatyzacji - Naprawa",
			"description":           "Wykonano naprawę klimatyzacji w biurze głównym",
			"communication_date":    time.Now().Add(-12 * time.Hour),
			"status":                "completed",
			"priority":              "high",
			"category":              "service_completion",
			"technician_name":       "Marek Nowak",
			"work_performed":        "Wymiana filtra, czyszczenie parownika, uzupełnienie czynnika",
			"duration":              180, // 3 hours
			"cost":                  850.00,
			"customer_satisfaction": 5,
			"requires_follow_up":    true,
			"follow_up_date":        time.Now().Add(7 * 24 * time.Hour),
		},
		{
			"id":                 4,
			"type":               "email",
			"title":              "Zapytanie o rozszerzenie systemu",
			"description":        "Klient pyta o możliwość rozszerzenia klimatyzacji na nowy budynek",
			"communication_date": time.Now().Add(-6 * time.Hour),
			"direction":          "inbound",
			"from_email":         "<EMAIL>",
			"to_email":           "<EMAIL>",
			"subject":            "Rozszerzenie systemu klimatyzacji - nowy budynek",
			"sentiment_score":    0.7,
			"intent":             "quote_request",
			"priority":           "medium",
			"category":           "sales_opportunity",
			"estimated_value":    75000.00,
			"key_topics":         []string{"rozszerzenie", "nowy-budynek", "klimatyzacja", "oferta"},
			"status":             "pending_response",
			"requires_follow_up": true,
			"follow_up_date":     time.Now().Add(24 * time.Hour),
		},
		{
			"id":                     5,
			"type":                   "transcription",
			"title":                  "Rozmowa - Planowanie rozbudowy",
			"description":            "Szczegółowa rozmowa o planach rozbudowy systemu HVAC",
			"communication_date":     time.Now().Add(-2 * time.Hour),
			"direction":              "outbound",
			"to_phone":               "+**************",
			"duration":               1260, // 21 minutes
			"file_name":              "call_outbound_48123456789_20241201_160015.m4a",
			"sentiment_score":        0.85,
			"intent":                 "sales_consultation",
			"priority":               "high",
			"category":               "business_development",
			"key_topics":             []string{"rozbudowa", "budżet", "timeline", "specyfikacja"},
			"action_items":           []string{"Przygotować szczegółową ofertę", "Wizyta techniczna", "Prezentacja rozwiązań"},
			"ai_summary":             "Bardzo pozytywna rozmowa o rozbudowie. Klient ma budżet 70-80k PLN, planuje realizację w Q1 2025.",
			"business_value":         75000.00,
			"conversion_probability": 0.78,
			"status":                 "analyzed",
			"requires_follow_up":     true,
			"follow_up_date":         time.Now().Add(3 * 24 * time.Hour),
		},
	}
}

// generateCustomerAIInsights - Generuj AI insights dla klienta
func generateCustomerAIInsights(customerID int64) []map[string]interface{} {
	return []map[string]interface{}{
		{
			"id":                 1,
			"type":               "prediction",
			"title":              "Wysokie prawdopodobieństwo zakupu",
			"description":        "Na podstawie analizy komunikacji i wzorców zachowań, prawdopodobieństwo zakupu nowego systemu HVAC wynosi 78%",
			"confidence":         0.78,
			"category":           "sales",
			"priority":           "high",
			"action_required":    true,
			"recommended_action": "Przygotować szczegółową ofertę na system klimatyzacji dla nowego budynku",
			"source":             "behavior_analysis",
			"model_version":      "gemma3-4b-v1.2",
			"estimated_value":    75000.00,
			"timeline":           "Q1 2025",
			"key_indicators":     []string{"Pozytywny sentiment", "Konkretne pytania o rozszerzenie", "Budżet zatwierdzony"},
			"created_at":         time.Now().Add(-1 * time.Hour),
		},
		{
			"id":                 2,
			"type":               "recommendation",
			"title":              "Proponowany program lojalnościowy",
			"description":        "Klient wykazuje cechy VIP customer. Rekomendujemy włączenie do programu premium z dedykowanym opiekunem",
			"confidence":         0.92,
			"category":           "customer_retention",
			"priority":           "medium",
			"action_required":    true,
			"recommended_action": "Zaproponować program VIP z priorytetowym serwisem i rabatami",
			"source":             "customer_scoring",
			"model_version":      "gemma3-4b-v1.2",
			"benefits":           []string{"Zwiększona lojalność", "Wyższa wartość klienta", "Pozytywne referencje"},
			"created_at":         time.Now().Add(-3 * time.Hour),
		},
		{
			"id":                 3,
			"type":               "alert",
			"title":              "Zbliżający się termin serwisu",
			"description":        "Analiza harmonogramu wskazuje na zbliżający się termin planowego serwisu klimatyzacji (za 60 dni)",
			"confidence":         0.95,
			"category":           "maintenance",
			"priority":           "medium",
			"action_required":    true,
			"recommended_action": "Skontaktować się z klientem w sprawie umówienia serwisu prewencyjnego",
			"source":             "maintenance_scheduling",
			"model_version":      "gemma3-4b-v1.2",
			"due_date":           time.Now().Add(60 * 24 * time.Hour),
			"equipment_affected": []string{"Samsung AR12TXHQASINEU", "Daikin FTXM35R"},
			"created_at":         time.Now().Add(-6 * time.Hour),
		},
		{
			"id":                 4,
			"type":               "pattern",
			"title":              "Wzorzec komunikacji biznesowej",
			"description":        "AI wykryło wzorzec komunikacji typowy dla klientów B2B o wysokiej wartości - szybkie odpowiedzi, konkretne pytania, decyzyjność",
			"confidence":         0.87,
			"category":           "behavior_analysis",
			"priority":           "low",
			"action_required":    false,
			"recommended_action": "Kontynuować profesjonalną komunikację z naciskiem na konkretne rozwiązania",
			"source":             "communication_analysis",
			"model_version":      "gemma3-4b-v1.2",
			"pattern_indicators": []string{"Średni czas odpowiedzi: 2.3h", "Konkretne pytania techniczne", "Szybkie podejmowanie decyzji"},
			"created_at":         time.Now().Add(-12 * time.Hour),
		},
		{
			"id":                  5,
			"type":                "opportunity",
			"title":               "Potencjał cross-selling",
			"description":         "Analiza potrzeb wskazuje na możliwość sprzedaży dodatkowych usług: monitoring IoT, inteligentne sterowanie",
			"confidence":          0.71,
			"category":            "sales",
			"priority":            "medium",
			"action_required":     true,
			"recommended_action":  "Zaproponować nowoczesne rozwiązania IoT i automatyki podczas przygotowywania oferty",
			"source":              "needs_analysis",
			"model_version":       "gemma3-4b-v1.2",
			"estimated_value":     15000.00,
			"products_suggested":  []string{"System monitoringu IoT", "Inteligentne sterowanie", "Aplikacja mobilna"},
			"success_probability": 0.65,
			"created_at":          time.Now().Add(-8 * time.Hour),
		},
	}
}
