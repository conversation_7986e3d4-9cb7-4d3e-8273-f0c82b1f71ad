package main

import (
	"encoding/json"
	"net/http"
	"strconv"
	"time"

	"github.com/gorilla/mux"
)

// 📁 DOCUMENT MANAGEMENT SYSTEM - Kompleksowe zarządzanie dokumentami klientów
// Archiwalne emaile, dokumenty, zdjęcia, raporty, umowy, faktury, certyfikaty

// handleGetCustomerDocuments - Pobierz wszystkie dokumenty klienta
func handleGetCustomerDocuments(w http.ResponseWriter, r *http.Request) {
	vars := mux.Vars(r)
	customerIDStr := vars["id"]

	customerID, err := strconv.ParseInt(customerIDStr, 10, 64)
	if err != nil {
		http.Error(w, "Invalid customer ID", http.StatusBadRequest)
		return
	}

	documents := generateCustomerDocuments(customerID)

	response := UnifiedCRMResponse{
		Data: documents,
		Meta: ResponseMeta{
			QueryTime:     "89ms",
			DataFreshness: "real-time",
		},
		UI: UIEnhancements{
			Actions: []QuickAction{
				{ID: "upload_document", Label: "Upload Document", Icon: "📤", Color: "#3498db"},
				{ID: "create_folder", Label: "Create Folder", Icon: "📁", Color: "#27ae60"},
				{ID: "archive_old", Label: "Archive Old", Icon: "📦", Color: "#95a5a6"},
				{ID: "search_documents", Label: "Search", Icon: "🔍", Color: "#f39c12"},
			},
		},
		Context: map[string]interface{}{
			"customer_id":     customerID,
			"total_documents": 156,
			"storage_used":    "2.3 GB",
			"last_backup":     time.Now().Add(-24 * time.Hour),
		},
		Timestamp: time.Now().Format(time.RFC3339),
	}

	w.Header().Set("Content-Type", "application/json")
	json.NewEncoder(w).Encode(response)
}

// handleGetDocumentCategories - Pobierz kategorie dokumentów
func handleGetDocumentCategories(w http.ResponseWriter, r *http.Request) {
	categories := map[string]interface{}{
		"categories": []map[string]interface{}{
			{
				"id":            "contracts",
				"name":          "Umowy i Kontrakty",
				"icon":          "📋",
				"color":         "#3498db",
				"count":         23,
				"description":   "Umowy serwisowe, kontrakty, aneksy",
				"subcategories": []string{"Umowy serwisowe", "Kontrakty instalacyjne", "Aneksy", "Gwarancje"},
			},
			{
				"id":            "invoices",
				"name":          "Faktury i Płatności",
				"icon":          "💰",
				"color":         "#27ae60",
				"count":         89,
				"description":   "Faktury, rachunki, potwierdzenia płatności",
				"subcategories": []string{"Faktury VAT", "Rachunki", "Potwierdzenia płatności", "Korekty"},
			},
			{
				"id":            "technical",
				"name":          "Dokumentacja Techniczna",
				"icon":          "🔧",
				"color":         "#e74c3c",
				"count":         45,
				"description":   "Schematy, instrukcje, certyfikaty",
				"subcategories": []string{"Schematy instalacji", "Instrukcje obsługi", "Certyfikaty", "Raporty techniczne"},
			},
			{
				"id":            "correspondence",
				"name":          "Korespondencja",
				"icon":          "📧",
				"color":         "#9b59b6",
				"count":         234,
				"description":   "Emaile, listy, notatki ze spotkań",
				"subcategories": []string{"Emaile archiwalne", "Listy oficjalne", "Notatki ze spotkań", "Protokoły"},
			},
			{
				"id":            "photos",
				"name":          "Zdjęcia i Media",
				"icon":          "📸",
				"color":         "#f39c12",
				"count":         67,
				"description":   "Zdjęcia instalacji, filmy, prezentacje",
				"subcategories": []string{"Zdjęcia przed", "Zdjęcia po", "Filmy instruktażowe", "Prezentacje"},
			},
			{
				"id":            "reports",
				"name":          "Raporty i Analizy",
				"icon":          "📊",
				"color":         "#1abc9c",
				"count":         34,
				"description":   "Raporty serwisowe, analizy wydajności",
				"subcategories": []string{"Raporty serwisowe", "Analizy wydajności", "Audyty energetyczne", "Pomiary"},
			},
			{
				"id":            "certificates",
				"name":          "Certyfikaty i Licencje",
				"icon":          "🏆",
				"color":         "#e67e22",
				"count":         12,
				"description":   "Certyfikaty, licencje, uprawnienia",
				"subcategories": []string{"Certyfikaty instalacji", "Licencje oprogramowania", "Uprawnienia", "Atesty"},
			},
			{
				"id":            "archive",
				"name":          "Archiwum",
				"icon":          "📦",
				"color":         "#95a5a6",
				"count":         156,
				"description":   "Stare dokumenty, kopie zapasowe",
				"subcategories": []string{"Dokumenty archiwalne", "Kopie zapasowe", "Stare wersje", "Usunięte"},
			},
		},
		"statistics": map[string]interface{}{
			"total_documents":      660,
			"total_size":           "8.7 GB",
			"documents_this_month": 23,
			"most_active_category": "correspondence",
			"storage_usage": map[string]interface{}{
				"used":       "8.7 GB",
				"available":  "41.3 GB",
				"percentage": 17.4,
			},
		},
	}

	response := UnifiedCRMResponse{
		Data: categories,
		Meta: ResponseMeta{
			QueryTime:     "45ms",
			DataFreshness: "real-time",
		},
		Context: map[string]interface{}{
			"view_type": "categories",
			"sort_by":   "name",
		},
		Timestamp: time.Now().Format(time.RFC3339),
	}

	w.Header().Set("Content-Type", "application/json")
	json.NewEncoder(w).Encode(response)
}

// handleGetArchivalEmails - Pobierz archiwalne emaile klienta
func handleGetArchivalEmails(w http.ResponseWriter, r *http.Request) {
	vars := mux.Vars(r)
	customerIDStr := vars["id"]

	customerID, err := strconv.ParseInt(customerIDStr, 10, 64)
	if err != nil {
		http.Error(w, "Invalid customer ID", http.StatusBadRequest)
		return
	}

	emails := generateArchivalEmails(customerID)

	response := UnifiedCRMResponse{
		Data: emails,
		Meta: ResponseMeta{
			QueryTime:     "156ms",
			DataFreshness: "real-time",
		},
		UI: UIEnhancements{
			Actions: []QuickAction{
				{ID: "export_emails", Label: "Export Emails", Icon: "📤", Color: "#3498db"},
				{ID: "search_emails", Label: "Search", Icon: "🔍", Color: "#f39c12"},
				{ID: "filter_by_date", Label: "Filter by Date", Icon: "📅", Color: "#9b59b6"},
				{ID: "archive_selected", Label: "Archive Selected", Icon: "📦", Color: "#95a5a6"},
			},
		},
		Context: map[string]interface{}{
			"customer_id":  customerID,
			"total_emails": 234,
			"date_range":   "2020-2024",
			"email_types":  []string{"incoming", "outgoing", "internal"},
		},
		Timestamp: time.Now().Format(time.RFC3339),
	}

	w.Header().Set("Content-Type", "application/json")
	json.NewEncoder(w).Encode(response)
}

// handleGetCustomerTimeline - Pobierz chronologiczną linię czasu klienta
func handleGetCustomerTimeline(w http.ResponseWriter, r *http.Request) {
	vars := mux.Vars(r)
	customerIDStr := vars["id"]

	customerID, err := strconv.ParseInt(customerIDStr, 10, 64)
	if err != nil {
		http.Error(w, "Invalid customer ID", http.StatusBadRequest)
		return
	}

	timeline := generateCustomerTimeline(customerID)

	response := UnifiedCRMResponse{
		Data: timeline,
		Meta: ResponseMeta{
			QueryTime:     "234ms",
			DataFreshness: "real-time",
		},
		UI: UIEnhancements{
			Actions: []QuickAction{
				{ID: "add_event", Label: "Add Event", Icon: "➕", Color: "#27ae60"},
				{ID: "filter_timeline", Label: "Filter", Icon: "🔍", Color: "#f39c12"},
				{ID: "export_timeline", Label: "Export", Icon: "📤", Color: "#3498db"},
				{ID: "timeline_settings", Label: "Settings", Icon: "⚙️", Color: "#95a5a6"},
			},
		},
		Context: map[string]interface{}{
			"customer_id":  customerID,
			"total_events": 89,
			"date_range":   "2020-2024",
			"event_types":  []string{"service", "communication", "document", "payment", "meeting"},
		},
		Timestamp: time.Now().Format(time.RFC3339),
	}

	w.Header().Set("Content-Type", "application/json")
	json.NewEncoder(w).Encode(response)
}

// generateCustomerDocuments - Generuj przykładowe dokumenty klienta
func generateCustomerDocuments(customerID int64) map[string]interface{} {
	return map[string]interface{}{
		"folders": []map[string]interface{}{
			{
				"id":           "contracts_2024",
				"name":         "Umowy 2024",
				"type":         "folder",
				"category":     "contracts",
				"created_date": time.Now().Add(-30 * 24 * time.Hour),
				"file_count":   8,
				"size":         "12.4 MB",
				"icon":         "📋",
			},
			{
				"id":           "invoices_q1_2024",
				"name":         "Faktury Q1 2024",
				"type":         "folder",
				"category":     "invoices",
				"created_date": time.Now().Add(-90 * 24 * time.Hour),
				"file_count":   23,
				"size":         "5.7 MB",
				"icon":         "💰",
			},
			{
				"id":           "installation_photos",
				"name":         "Zdjęcia Instalacji",
				"type":         "folder",
				"category":     "photos",
				"created_date": time.Now().Add(-60 * 24 * time.Hour),
				"file_count":   45,
				"size":         "234.5 MB",
				"icon":         "📸",
			},
		},
		"recent_documents": []map[string]interface{}{
			{
				"id":            "doc_001",
				"name":          "Umowa serwisowa 2024-001.pdf",
				"type":          "file",
				"category":      "contracts",
				"size":          "2.3 MB",
				"created_date":  time.Now().Add(-2 * 24 * time.Hour),
				"modified_date": time.Now().Add(-1 * 24 * time.Hour),
				"author":        "Jan Kowalski",
				"status":        "signed",
				"icon":          "📄",
				"tags":          []string{"umowa", "serwis", "2024", "podpisana"},
			},
			{
				"id":            "doc_002",
				"name":          "Raport serwisowy AC-001.pdf",
				"type":          "file",
				"category":      "reports",
				"size":          "1.8 MB",
				"created_date":  time.Now().Add(-5 * 24 * time.Hour),
				"modified_date": time.Now().Add(-5 * 24 * time.Hour),
				"author":        "Technik Serwisowy",
				"status":        "completed",
				"icon":          "📊",
				"tags":          []string{"raport", "serwis", "klimatyzacja", "ukończony"},
			},
			{
				"id":            "doc_003",
				"name":          "Zdjęcia_instalacji_2024_05_27.zip",
				"type":          "file",
				"category":      "photos",
				"size":          "45.2 MB",
				"created_date":  time.Now().Add(-3 * 24 * time.Hour),
				"modified_date": time.Now().Add(-3 * 24 * time.Hour),
				"author":        "Instalator",
				"status":        "archived",
				"icon":          "🗜️",
				"tags":          []string{"zdjęcia", "instalacja", "archiwum"},
			},
		},
		"document_stats": map[string]interface{}{
			"total_documents": 156,
			"total_size":      "2.3 GB",
			"by_category": map[string]interface{}{
				"contracts":      23,
				"invoices":       89,
				"technical":      45,
				"correspondence": 234,
				"photos":         67,
				"reports":        34,
				"certificates":   12,
				"archive":        156,
			},
			"recent_activity": []map[string]interface{}{
				{"action": "uploaded", "document": "Umowa serwisowa 2024-001.pdf", "date": time.Now().Add(-2 * 24 * time.Hour)},
				{"action": "modified", "document": "Raport serwisowy AC-001.pdf", "date": time.Now().Add(-5 * 24 * time.Hour)},
				{"action": "archived", "document": "Stare faktury 2023", "date": time.Now().Add(-7 * 24 * time.Hour)},
			},
		},
	}
}

// generateArchivalEmails - Generuj archiwalne emaile klienta
func generateArchivalEmails(customerID int64) map[string]interface{} {
	return map[string]interface{}{
		"email_threads": []map[string]interface{}{
			{
				"thread_id":     "thread_001",
				"subject":       "Serwis klimatyzacji - pilne",
				"participants":  []string{"<EMAIL>", "<EMAIL>", "<EMAIL>"},
				"message_count": 8,
				"start_date":    time.Now().Add(-30 * 24 * time.Hour),
				"last_message":  time.Now().Add(-25 * 24 * time.Hour),
				"status":        "resolved",
				"priority":      "high",
				"category":      "service_request",
				"tags":          []string{"pilne", "klimatyzacja", "serwis", "rozwiązane"},
				"attachments":   3,
			},
			{
				"thread_id":     "thread_002",
				"subject":       "Oferta na nową instalację",
				"participants":  []string{"<EMAIL>", "<EMAIL>"},
				"message_count": 12,
				"start_date":    time.Now().Add(-60 * 24 * time.Hour),
				"last_message":  time.Now().Add(-45 * 24 * time.Hour),
				"status":        "closed_won",
				"priority":      "medium",
				"category":      "sales",
				"tags":          []string{"oferta", "instalacja", "sprzedaż", "wygrana"},
				"attachments":   5,
			},
			{
				"thread_id":     "thread_003",
				"subject":       "Pytania techniczne - pompa ciepła",
				"participants":  []string{"<EMAIL>", "<EMAIL>", "<EMAIL>"},
				"message_count": 15,
				"start_date":    time.Now().Add(-90 * 24 * time.Hour),
				"last_message":  time.Now().Add(-85 * 24 * time.Hour),
				"status":        "resolved",
				"priority":      "medium",
				"category":      "technical_support",
				"tags":          []string{"pompa ciepła", "techniczne", "wsparcie", "rozwiązane"},
				"attachments":   7,
			},
		},
		"email_statistics": map[string]interface{}{
			"total_emails":      234,
			"total_threads":     45,
			"total_attachments": 89,
			"by_category": map[string]interface{}{
				"service_request":   67,
				"sales":             45,
				"technical_support": 78,
				"billing":           23,
				"general":           21,
			},
			"by_status": map[string]interface{}{
				"resolved":    156,
				"closed_won":  34,
				"closed_lost": 12,
				"pending":     8,
				"archived":    24,
			},
			"by_priority": map[string]interface{}{
				"high":   23,
				"medium": 145,
				"low":    66,
			},
		},
		"recent_emails": []map[string]interface{}{
			{
				"email_id":         "email_001",
				"subject":          "Potwierdzenie wizyty serwisowej",
				"from":             "<EMAIL>",
				"to":               "<EMAIL>",
				"date":             time.Now().Add(-2 * 24 * time.Hour),
				"type":             "outgoing",
				"status":           "delivered",
				"category":         "service_request",
				"has_attachments":  true,
				"attachment_count": 2,
				"preview":          "Dzień dobry, potwierdzamy wizytę serwisową na dzień 27.05.2024...",
			},
			{
				"email_id":         "email_002",
				"subject":          "Faktura VAT 2024/05/123",
				"from":             "<EMAIL>",
				"to":               "<EMAIL>",
				"date":             time.Now().Add(-5 * 24 * time.Hour),
				"type":             "outgoing",
				"status":           "delivered",
				"category":         "billing",
				"has_attachments":  true,
				"attachment_count": 1,
				"preview":          "Przesyłamy fakturę VAT za wykonane usługi serwisowe...",
			},
		},
	}
}

// generateCustomerTimeline - Generuj chronologiczną linię czasu klienta
func generateCustomerTimeline(customerID int64) map[string]interface{} {
	return map[string]interface{}{
		"timeline_events": []map[string]interface{}{
			{
				"event_id":     "event_001",
				"date":         time.Now().Add(-2 * 24 * time.Hour),
				"type":         "service",
				"title":        "Serwis klimatyzacji wykonany",
				"description":  "Przeprowadzono przegląd i konserwację systemu klimatyzacji",
				"status":       "completed",
				"priority":     "medium",
				"category":     "maintenance",
				"participants": []string{"Technik Jan Kowalski", "Klient"},
				"documents":    []string{"Raport serwisowy", "Protokół odbioru"},
				"location":     "Warszawa, ul. Przykładowa 123",
				"duration":     "2 godziny",
				"cost":         450.00,
				"icon":         "🔧",
				"color":        "#27ae60",
			},
			{
				"event_id":     "event_002",
				"date":         time.Now().Add(-7 * 24 * time.Hour),
				"type":         "communication",
				"title":        "Rozmowa telefoniczna - umówienie serwisu",
				"description":  "Klient zgłosił problem z klimatyzacją, umówiono wizytę serwisową",
				"status":       "completed",
				"priority":     "high",
				"category":     "customer_service",
				"participants": []string{"Konsultant Anna Nowak", "Klient"},
				"documents":    []string{"Notatka z rozmowy"},
				"location":     "Telefonicznie",
				"duration":     "15 minut",
				"cost":         0.00,
				"icon":         "📞",
				"color":        "#3498db",
			},
			{
				"event_id":     "event_003",
				"date":         time.Now().Add(-30 * 24 * time.Hour),
				"type":         "document",
				"title":        "Podpisanie umowy serwisowej",
				"description":  "Klient podpisał roczną umowę serwisową na obsługę systemu HVAC",
				"status":       "completed",
				"priority":     "high",
				"category":     "contract",
				"participants": []string{"Sprzedawca Piotr Wiśniewski", "Klient"},
				"documents":    []string{"Umowa serwisowa 2024-001", "Regulamin"},
				"location":     "Biuro klienta",
				"duration":     "1 godzina",
				"cost":         2400.00,
				"icon":         "📋",
				"color":        "#9b59b6",
			},
			{
				"event_id":     "event_004",
				"date":         time.Now().Add(-45 * 24 * time.Hour),
				"type":         "payment",
				"title":        "Płatność za instalację",
				"description":  "Otrzymano płatność za wykonaną instalację systemu klimatyzacji",
				"status":       "completed",
				"priority":     "medium",
				"category":     "financial",
				"participants": []string{"Księgowość", "Klient"},
				"documents":    []string{"Faktura VAT", "Potwierdzenie płatności"},
				"location":     "Przelew bankowy",
				"duration":     "-",
				"cost":         15600.00,
				"icon":         "💰",
				"color":        "#f39c12",
			},
			{
				"event_id":     "event_005",
				"date":         time.Now().Add(-60 * 24 * time.Hour),
				"type":         "meeting",
				"title":        "Spotkanie projektowe",
				"description":  "Spotkanie w sprawie projektu instalacji systemu klimatyzacji",
				"status":       "completed",
				"priority":     "high",
				"category":     "project",
				"participants": []string{"Projektant", "Klient", "Kierownik projektu"},
				"documents":    []string{"Projekt instalacji", "Kosztorys", "Harmonogram"},
				"location":     "Biuro projektowe",
				"duration":     "2.5 godziny",
				"cost":         0.00,
				"icon":         "👥",
				"color":        "#e74c3c",
			},
		},
		"timeline_statistics": map[string]interface{}{
			"total_events":      89,
			"events_this_month": 12,
			"by_type": map[string]interface{}{
				"service":       23,
				"communication": 34,
				"document":      15,
				"payment":       8,
				"meeting":       9,
			},
			"by_category": map[string]interface{}{
				"maintenance":      23,
				"customer_service": 34,
				"contract":         15,
				"financial":        8,
				"project":          9,
			},
			"total_value":     45600.00,
			"avg_event_value": 512.36,
		},
		"upcoming_events": []map[string]interface{}{
			{
				"event_id":           "event_future_001",
				"date":               time.Now().Add(7 * 24 * time.Hour),
				"type":               "service",
				"title":              "Planowany przegląd roczny",
				"description":        "Roczny przegląd systemu klimatyzacji zgodnie z umową serwisową",
				"status":             "scheduled",
				"priority":           "medium",
				"category":           "maintenance",
				"participants":       []string{"Technik", "Klient"},
				"estimated_duration": "2 godziny",
				"estimated_cost":     350.00,
				"icon":               "🔧",
				"color":              "#27ae60",
			},
		},
	}
}
