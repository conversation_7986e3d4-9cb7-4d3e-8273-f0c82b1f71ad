syntax = "proto3";

package api.equipment.v1;

import "google/api/annotations.proto";
import "google/protobuf/timestamp.proto";
import "google/protobuf/struct.proto";

option go_package = "gobackend-hvac-kratos/api/equipment/v1;v1";

// 🏭 Equipment Registry Service - Comprehensive Equipment Management
service EquipmentService {
  // Equipment CRUD Operations
  rpc CreateEquipment(CreateEquipmentRequest) returns (CreateEquipmentResponse) {
    option (google.api.http) = {
      post: "/api/v1/equipment"
      body: "*"
    };
  }

  rpc GetEquipment(GetEquipmentRequest) returns (GetEquipmentResponse) {
    option (google.api.http) = {
      get: "/api/v1/equipment/{id}"
    };
  }

  rpc ListEquipment(ListEquipmentRequest) returns (ListEquipmentResponse) {
    option (google.api.http) = {
      get: "/api/v1/equipment"
    };
  }

  rpc UpdateEquipment(UpdateEquipmentRequest) returns (UpdateEquipmentResponse) {
    option (google.api.http) = {
      put: "/api/v1/equipment/{id}"
      body: "*"
    };
  }

  rpc DeleteEquipment(DeleteEquipmentRequest) returns (DeleteEquipmentResponse) {
    option (google.api.http) = {
      delete: "/api/v1/equipment/{id}"
    };
  }

  // Health Monitoring
  rpc GetEquipmentHealth(GetEquipmentHealthRequest) returns (GetEquipmentHealthResponse) {
    option (google.api.http) = {
      get: "/api/v1/equipment/{id}/health"
    };
  }

  rpc UpdateEquipmentHealth(UpdateEquipmentHealthRequest) returns (UpdateEquipmentHealthResponse) {
    option (google.api.http) = {
      post: "/api/v1/equipment/{id}/health"
      body: "*"
    };
  }

  // Maintenance Management
  rpc ScheduleMaintenance(ScheduleMaintenanceRequest) returns (ScheduleMaintenanceResponse) {
    option (google.api.http) = {
      post: "/api/v1/equipment/{id}/maintenance/schedule"
      body: "*"
    };
  }

  rpc GetMaintenanceHistory(GetMaintenanceHistoryRequest) returns (GetMaintenanceHistoryResponse) {
    option (google.api.http) = {
      get: "/api/v1/equipment/{id}/maintenance/history"
    };
  }

  // Parts Management
  rpc GetEquipmentParts(GetEquipmentPartsRequest) returns (GetEquipmentPartsResponse) {
    option (google.api.http) = {
      get: "/api/v1/equipment/{id}/parts"
    };
  }

  rpc UpdatePartStatus(UpdatePartStatusRequest) returns (UpdatePartStatusResponse) {
    option (google.api.http) = {
      put: "/api/v1/equipment/{id}/parts/{part_id}"
      body: "*"
    };
  }

  // Analytics and Reporting
  rpc GetEquipmentAnalytics(GetEquipmentAnalyticsRequest) returns (GetEquipmentAnalyticsResponse) {
    option (google.api.http) = {
      get: "/api/v1/equipment/{id}/analytics"
    };
  }

  rpc GetFleetOverview(GetFleetOverviewRequest) returns (GetFleetOverviewResponse) {
    option (google.api.http) = {
      get: "/api/v1/equipment/fleet/overview"
    };
  }

  // QR Code and Identification
  rpc GenerateQRCode(GenerateQRCodeRequest) returns (GenerateQRCodeResponse) {
    option (google.api.http) = {
      post: "/api/v1/equipment/{id}/qr-code"
      body: "*"
    };
  }
}

// ============================================================================
// EQUIPMENT MODELS
// ============================================================================

message Equipment {
  int64 id = 1;
  int64 customer_id = 2;
  string name = 3;
  string type = 4; // hvac_unit, heat_pump, air_conditioner, furnace, etc.
  string brand = 5;
  string model = 6;
  string serial_number = 7;
  google.protobuf.Timestamp installation_date = 8;
  google.protobuf.Timestamp warranty_expiry = 9;
  string location = 10; // Physical location description
  double latitude = 11;
  double longitude = 12;
  EquipmentStatus status = 13;
  EquipmentHealth health = 14;
  google.protobuf.Struct specifications = 15; // Technical specifications
  google.protobuf.Struct metadata = 16; // Additional metadata
  google.protobuf.Timestamp created_at = 17;
  google.protobuf.Timestamp updated_at = 18;
}

enum EquipmentStatus {
  EQUIPMENT_STATUS_UNSPECIFIED = 0;
  EQUIPMENT_STATUS_ACTIVE = 1;
  EQUIPMENT_STATUS_INACTIVE = 2;
  EQUIPMENT_STATUS_MAINTENANCE = 3;
  EQUIPMENT_STATUS_RETIRED = 4;
  EQUIPMENT_STATUS_FAULTY = 5;
}

message EquipmentHealth {
  double health_score = 1; // 0.0 to 1.0
  string health_status = 2; // excellent, good, fair, poor, critical
  repeated HealthMetric metrics = 3;
  google.protobuf.Timestamp last_assessment = 4;
  string assessment_method = 5; // manual, iot_sensors, predictive_model
}

message HealthMetric {
  string name = 1;
  double value = 2;
  string unit = 3;
  double threshold_min = 4;
  double threshold_max = 5;
  string status = 6; // normal, warning, critical
}

// ============================================================================
// REQUEST/RESPONSE MESSAGES
// ============================================================================

// Equipment CRUD
message CreateEquipmentRequest {
  int64 customer_id = 1;
  string name = 2;
  string type = 3;
  string brand = 4;
  string model = 5;
  string serial_number = 6;
  google.protobuf.Timestamp installation_date = 7;
  google.protobuf.Timestamp warranty_expiry = 8;
  string location = 9;
  double latitude = 10;
  double longitude = 11;
  google.protobuf.Struct specifications = 12;
  google.protobuf.Struct metadata = 13;
}

message CreateEquipmentResponse {
  Equipment equipment = 1;
}

message GetEquipmentRequest {
  int64 id = 1;
}

message GetEquipmentResponse {
  Equipment equipment = 1;
}

message ListEquipmentRequest {
  int32 page = 1;
  int32 page_size = 2;
  int64 customer_id = 3;
  string type = 4;
  string status = 5;
  string health_status = 6;
}

message ListEquipmentResponse {
  repeated Equipment equipment = 1;
  int32 total_count = 2;
  int32 page = 3;
  int32 page_size = 4;
}

message UpdateEquipmentRequest {
  int64 id = 1;
  string name = 2;
  string location = 3;
  double latitude = 4;
  double longitude = 5;
  EquipmentStatus status = 6;
  google.protobuf.Struct specifications = 7;
  google.protobuf.Struct metadata = 8;
}

message UpdateEquipmentResponse {
  Equipment equipment = 1;
}

message DeleteEquipmentRequest {
  int64 id = 1;
}

message DeleteEquipmentResponse {
  bool success = 1;
}
