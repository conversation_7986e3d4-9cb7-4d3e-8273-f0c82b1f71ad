-- 🏭 Equipment Registry Database Migration
-- GoBackend-<PERSON>ratos HVAC CRM System
-- Migration: 003_create_equipment_tables.sql

-- Equipment table
CREATE TABLE IF NOT EXISTS equipment (
    id BIGSERIAL PRIMARY KEY,
    customer_id BIGINT NOT NULL REFERENCES customers(id) ON DELETE CASCADE,
    name VARCHAR(255) NOT NULL,
    type VARCHAR(100) NOT NULL,
    brand VARCHAR(100),
    model VARCHAR(100),
    serial_number VARCHAR(100) UNIQUE,
    installation_date TIMESTAMP,
    warranty_expiry TIMESTAMP,
    location VARCHAR(500),
    latitude DECIMAL(10, 8),
    longitude DECIMAL(11, 8),
    status VARCHAR(50) DEFAULT 'active' CHECK (status IN ('active', 'inactive', 'maintenance', 'retired', 'faulty')),
    health_score DECIMAL(3, 2) DEFAULT 1.0 CHECK (health_score >= 0 AND health_score <= 1),
    health_status VARCHAR(50) DEFAULT 'excellent' CHECK (health_status IN ('excellent', 'good', 'fair', 'poor', 'critical')),
    specifications JSONB,
    metadata JSONB,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- Health metrics table
CREATE TABLE IF NOT EXISTS health_metrics (
    id BIGSERIAL PRIMARY KEY,
    equipment_id BIGINT NOT NULL REFERENCES equipment(id) ON DELETE CASCADE,
    name VARCHAR(100) NOT NULL,
    value DECIMAL(15, 6),
    unit VARCHAR(50),
    threshold_min DECIMAL(15, 6),
    threshold_max DECIMAL(15, 6),
    status VARCHAR(50) CHECK (status IN ('normal', 'warning', 'critical')),
    recorded_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- Maintenance records table
CREATE TABLE IF NOT EXISTS maintenance_records (
    id BIGSERIAL PRIMARY KEY,
    equipment_id BIGINT NOT NULL REFERENCES equipment(id) ON DELETE CASCADE,
    maintenance_type VARCHAR(50) NOT NULL CHECK (maintenance_type IN ('preventive', 'corrective', 'emergency')),
    performed_date TIMESTAMP NOT NULL,
    description TEXT,
    technician_id BIGINT,
    parts_used JSONB,
    actual_duration_minutes INTEGER,
    status VARCHAR(50) CHECK (status IN ('scheduled', 'in_progress', 'completed', 'cancelled')),
    notes TEXT,
    cost DECIMAL(10, 2) DEFAULT 0,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- Maintenance schedules table
CREATE TABLE IF NOT EXISTS maintenance_schedules (
    id BIGSERIAL PRIMARY KEY,
    equipment_id BIGINT NOT NULL REFERENCES equipment(id) ON DELETE CASCADE,
    maintenance_type VARCHAR(50) NOT NULL CHECK (maintenance_type IN ('preventive', 'corrective', 'emergency')),
    scheduled_date TIMESTAMP NOT NULL,
    description TEXT,
    technician_id BIGINT,
    required_parts JSONB,
    estimated_duration_minutes INTEGER,
    status VARCHAR(50) DEFAULT 'scheduled' CHECK (status IN ('scheduled', 'in_progress', 'completed', 'cancelled')),
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- Equipment parts table
CREATE TABLE IF NOT EXISTS equipment_parts (
    id BIGSERIAL PRIMARY KEY,
    equipment_id BIGINT NOT NULL REFERENCES equipment(id) ON DELETE CASCADE,
    name VARCHAR(255) NOT NULL,
    part_number VARCHAR(100),
    manufacturer VARCHAR(100),
    installation_date TIMESTAMP,
    warranty_expiry TIMESTAMP,
    status VARCHAR(50) DEFAULT 'new' CHECK (status IN ('new', 'good', 'worn', 'needs_replacement', 'replaced')),
    cost DECIMAL(10, 2) DEFAULT 0,
    supplier VARCHAR(255),
    specifications JSONB,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- Create indexes for better performance
CREATE INDEX IF NOT EXISTS idx_equipment_customer_id ON equipment(customer_id);
CREATE INDEX IF NOT EXISTS idx_equipment_type ON equipment(type);
CREATE INDEX IF NOT EXISTS idx_equipment_status ON equipment(status);
CREATE INDEX IF NOT EXISTS idx_equipment_health_status ON equipment(health_status);
CREATE INDEX IF NOT EXISTS idx_equipment_serial_number ON equipment(serial_number);

CREATE INDEX IF NOT EXISTS idx_health_metrics_equipment_id ON health_metrics(equipment_id);
CREATE INDEX IF NOT EXISTS idx_health_metrics_name ON health_metrics(name);
CREATE INDEX IF NOT EXISTS idx_health_metrics_recorded_at ON health_metrics(recorded_at);

CREATE INDEX IF NOT EXISTS idx_maintenance_records_equipment_id ON maintenance_records(equipment_id);
CREATE INDEX IF NOT EXISTS idx_maintenance_records_type ON maintenance_records(maintenance_type);
CREATE INDEX IF NOT EXISTS idx_maintenance_records_performed_date ON maintenance_records(performed_date);
CREATE INDEX IF NOT EXISTS idx_maintenance_records_technician_id ON maintenance_records(technician_id);

CREATE INDEX IF NOT EXISTS idx_maintenance_schedules_equipment_id ON maintenance_schedules(equipment_id);
CREATE INDEX IF NOT EXISTS idx_maintenance_schedules_scheduled_date ON maintenance_schedules(scheduled_date);
CREATE INDEX IF NOT EXISTS idx_maintenance_schedules_technician_id ON maintenance_schedules(technician_id);

CREATE INDEX IF NOT EXISTS idx_equipment_parts_equipment_id ON equipment_parts(equipment_id);
CREATE INDEX IF NOT EXISTS idx_equipment_parts_status ON equipment_parts(status);

-- Create triggers for updated_at timestamps
CREATE OR REPLACE FUNCTION update_updated_at_column()
RETURNS TRIGGER AS $$
BEGIN
    NEW.updated_at = CURRENT_TIMESTAMP;
    RETURN NEW;
END;
$$ language 'plpgsql';

CREATE TRIGGER update_equipment_updated_at BEFORE UPDATE ON equipment
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_maintenance_records_updated_at BEFORE UPDATE ON maintenance_records
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_maintenance_schedules_updated_at BEFORE UPDATE ON maintenance_schedules
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_equipment_parts_updated_at BEFORE UPDATE ON equipment_parts
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

-- Insert sample equipment data for testing
INSERT INTO equipment (customer_id, name, type, brand, model, serial_number, location, health_score, health_status) VALUES
(1, 'Main HVAC Unit - Building A', 'hvac_unit', 'Carrier', 'WeatherExpert 48TJD', 'CAR-48TJD-001', 'Building A - Roof', 0.95, 'excellent'),
(1, 'Heat Pump - Building B', 'heat_pump', 'Trane', 'XV20i', 'TRA-XV20I-002', 'Building B - Mechanical Room', 0.87, 'good'),
(2, 'Air Conditioner - Office', 'air_conditioner', 'Daikin', 'VRV IV', 'DAI-VRV4-003', 'Office - 2nd Floor', 0.72, 'fair')
ON CONFLICT (serial_number) DO NOTHING;

-- Insert sample health metrics
INSERT INTO health_metrics (equipment_id, name, value, unit, threshold_min, threshold_max, status) VALUES
(1, 'Temperature', 22.5, '°C', 18.0, 26.0, 'normal'),
(1, 'Pressure', 2.1, 'bar', 1.8, 2.5, 'normal'),
(1, 'Vibration', 0.3, 'mm/s', 0.0, 1.0, 'normal'),
(2, 'Temperature', 24.1, '°C', 18.0, 26.0, 'normal'),
(2, 'Efficiency', 85.2, '%', 80.0, 100.0, 'normal'),
(3, 'Temperature', 28.5, '°C', 18.0, 26.0, 'warning')
ON CONFLICT DO NOTHING;

-- Insert sample maintenance schedules
INSERT INTO maintenance_schedules (equipment_id, maintenance_type, scheduled_date, description, estimated_duration_minutes) VALUES
(1, 'preventive', CURRENT_TIMESTAMP + INTERVAL '30 days', 'Quarterly maintenance check', 120),
(2, 'preventive', CURRENT_TIMESTAMP + INTERVAL '45 days', 'Heat pump inspection and cleaning', 90),
(3, 'corrective', CURRENT_TIMESTAMP + INTERVAL '7 days', 'Temperature sensor calibration', 60)
ON CONFLICT DO NOTHING;

-- Insert sample equipment parts
INSERT INTO equipment_parts (equipment_id, name, part_number, manufacturer, status, cost) VALUES
(1, 'Air Filter', 'CAR-FILTER-001', 'Carrier', 'good', 45.99),
(1, 'Compressor', 'CAR-COMP-001', 'Carrier', 'good', 1250.00),
(2, 'Heat Exchanger', 'TRA-HEX-002', 'Trane', 'good', 850.00),
(3, 'Temperature Sensor', 'DAI-TEMP-003', 'Daikin', 'needs_replacement', 125.50)
ON CONFLICT DO NOTHING;

COMMENT ON TABLE equipment IS 'HVAC equipment registry with lifecycle tracking';
COMMENT ON TABLE health_metrics IS 'Equipment health monitoring data';
COMMENT ON TABLE maintenance_records IS 'Historical maintenance activities';
COMMENT ON TABLE maintenance_schedules IS 'Scheduled maintenance activities';
COMMENT ON TABLE equipment_parts IS 'Equipment parts and components tracking';
